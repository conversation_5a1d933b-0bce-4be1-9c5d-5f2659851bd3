
# -*- coding: utf-8 -*-
"""
(最終修正版)
這個腳本使用 Selenium 和 BeautifulSoup 自動化登入特定網頁，
並依序點擊不同的標籤 (Tab)，提取每個標籤下的地點及人員數量，
然後點擊每個地點進入詳細頁面進行檢查。
腳本使用 ttkbootstrap 建立一個優化後的 UI 介面，顯示執行日誌、倒數計時和結果表格。
設定和憑證從 config.ini 檔案讀取。

主要功能:
- UI 介面新增「地點選擇」區塊，可根據勾選的地點執行任務。
- 提供「開始排程」按鈕 (定時排程執行)、「TEST」按鈕 (立即執行一次) 和「停止執行」按鈕。
- 在地點詳細頁面，會根據"開始/結束疏散"按鈕觸發不同的處理流程，包括進入ERC頁面。
- ERC頁面會遍歷所有部門，提取數據並檢查"待確認人"。
- "待確認人" > 0 時，會進入詳情頁執行點擊與復原操作。
- 提供「匯出結果」按鈕，可將結果匯出為CSV。
- 修正了排程器無限循環問題，並整合了地點選擇邏輯。
"""

# 匯入 ttkbootstrap 和其常數
import ttkbootstrap as tb
from ttkbootstrap.constants import *
from ttkbootstrap.scrolled import ScrolledFrame # 新增匯入 ScrolledFrame
import tkinter as tk
from tkinter import scrolledtext
from tkinter import messagebox
from tkinter import filedialog
import threading
import queue
import configparser
import time
import sys
import os
import datetime
import csv
import zeep
import json
import base64
import requests

# Selenium 和 BeautifulSoup 相關匯入
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, StaleElementReferenceException, WebDriverException
from selenium.webdriver.chrome.options import Options
from bs4 import BeautifulSoup

# 圖片處理相關匯入
from PIL import Image, ImageDraw, ImageFont
import io
try:
    import cv2
    import numpy as np
    CV2_AVAILABLE = True
except ImportError:
    CV2_AVAILABLE = False
    print("Warning: OpenCV not available, using PIL fallback for image processing")

# 導入登入處理類別
from login_handler import LoginHandler

# ============================
# ====== 圖片合併處理類別 ======
class ImageMerger:
    """圖片合併處理類別 - 使用 PIL 實現"""

    def __init__(self):
        self.max_size_kb = 800
        self.max_dimension_per_cell = 700
        self.max_cols = 4

    def create_folder_if_not_exists(self, folder):
        """檢查資料夾是否存在，若不存在則建立"""
        if not os.path.exists(folder):
            os.makedirs(folder)

    def compress_image_pil(self, pil_image, max_size_kb, initial_quality=95):
        """使用 PIL 壓縮圖片"""
        max_size_bytes = max_size_kb * 1024

        # 嘗試不同的品質設定
        for quality in range(initial_quality, 10, -5):
            buffer = io.BytesIO()
            pil_image.save(buffer, format='JPEG', quality=quality, optimize=True)
            size_bytes = buffer.tell()

            if size_bytes <= max_size_bytes:
                buffer.seek(0)
                return True, buffer.getvalue()

        # 如果品質降低還是太大，嘗試縮小尺寸
        scale_factor = 0.8
        current_image = pil_image.copy()

        for attempt in range(10):  # 最多嘗試10次縮放
            new_width = int(current_image.width * scale_factor)
            new_height = int(current_image.height * scale_factor)

            if new_width < 50 or new_height < 50:
                break

            current_image = current_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

            for quality in range(95, 10, -10):
                buffer = io.BytesIO()
                current_image.save(buffer, format='JPEG', quality=quality, optimize=True)
                size_bytes = buffer.tell()

                if size_bytes <= max_size_bytes:
                    buffer.seek(0)
                    return True, buffer.getvalue()

        # 最後嘗試，使用最低品質
        buffer = io.BytesIO()
        current_image.save(buffer, format='JPEG', quality=10, optimize=True)
        buffer.seek(0)
        return False, buffer.getvalue()

    def resize_image_pil(self, pil_image, max_dimension):
        """使用 PIL 縮放圖片"""
        width, height = pil_image.size

        if width <= max_dimension and height <= max_dimension:
            return pil_image

        # 計算縮放比例
        scale = max_dimension / max(width, height)
        new_width = int(width * scale)
        new_height = int(height * scale)

        return pil_image.resize((new_width, new_height), Image.Resampling.LANCZOS)

    def merge_images_pil(self, image_paths):
        """使用 PIL 合併圖片"""
        if not image_paths:
            return None

        # 載入並預處理圖片
        images = []
        for path in image_paths:
            try:
                with Image.open(path) as img:
                    # 轉換為 RGB 模式
                    if img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 縮放圖片
                    img = self.resize_image_pil(img, self.max_dimension_per_cell)
                    images.append(img.copy())
            except Exception as e:
                print(f"Failed to load image {path}: {e}")
                continue

        if not images:
            return None

        if len(images) == 1:
            return images[0]

        # 計算網格布局
        rows = (len(images) + self.max_cols - 1) // self.max_cols

        # 找出每行每列的最大尺寸
        cell_width = max(img.width for img in images)
        cell_height = max(img.height for img in images)

        # 計算畫布大小
        canvas_width = min(len(images), self.max_cols) * cell_width
        canvas_height = rows * cell_height

        # 創建白色畫布
        canvas = Image.new('RGB', (canvas_width, canvas_height), 'white')

        # 將圖片貼到畫布上
        for idx, img in enumerate(images):
            row = idx // self.max_cols
            col = idx % self.max_cols

            x = col * cell_width
            y = row * cell_height

            # 將圖片置中貼上
            x_offset = (cell_width - img.width) // 2
            y_offset = (cell_height - img.height) // 2

            canvas.paste(img, (x + x_offset, y + y_offset))

        return canvas

    def merge_screenshots_to_output(self, session_folder):
        """將截圖合併並輸出到 pic_output 資料夾"""
        try:
            # 創建輸出資料夾
            output_folder = "pic_output"
            self.create_folder_if_not_exists(output_folder)

            # 獲取所有截圖文件
            if not os.path.exists(session_folder):
                print(f"Session folder not found: {session_folder}")
                return False

            image_paths = sorted([
                os.path.join(session_folder, f) for f in os.listdir(session_folder)
                if os.path.isfile(os.path.join(session_folder, f)) and f.lower().endswith(('.png', '.jpg', '.jpeg'))
            ])

            if not image_paths:
                print("No images found to merge")
                return False

            print(f"Found {len(image_paths)} images to merge")

            # 合併圖片
            merged_image = self.merge_images_pil(image_paths)
            if merged_image is None:
                print("Failed to merge images")
                return False

            # 壓縮並保存
            success, final_image_bytes = self.compress_image_pil(merged_image, self.max_size_kb)

            # 生成文件名
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 保存 pic.jpg (會被覆蓋)
            pic_output_path = os.path.join(output_folder, "pic.jpg")
            with open(pic_output_path, 'wb') as f:
                f.write(final_image_bytes)

            # 保存帶時間戳的文件 (保存用)
            timestamped_output_path = os.path.join(output_folder, f"pic_{timestamp}.jpg")
            with open(timestamped_output_path, 'wb') as f:
                f.write(final_image_bytes)

            actual_size_kb = len(final_image_bytes) / 1024
            print(f"Merged image saved: {pic_output_path} and {timestamped_output_path}")
            print(f"Final size: {actual_size_kb:.2f}KB")

            return True

        except Exception as e:
            print(f"Error merging screenshots: {e}")
            return False

# ============================
# ====== 新增: 地點資料結構 ======
# ============================
SITE_DATA = {
    '廠區': ['華亞', '龍科', '龍潭', '新竹GRC', '新竹ATC', '新竹L3C', '后里', '台中I', '台中II/III', '台南', '高雄'],
    '辦公室': ['台北1辦公室', '台北2辦公室', '高雄辦公室'],
    '宿舍': ['龍潭宿舍', '麻布宿舍', '后里宿舍', '台中宿舍'],
    '關係企業': ['達擎ADP', '友達數位ADT', '友達宇沛AET', '瑞鼎RAD', '友達晶彩ACC']
}

# 建立一個從地點反向查找標籤的地圖，方便後續邏輯使用
SITE_TO_TAB_MAP = {site: tab for tab, sites in SITE_DATA.items() for site in sites}


# ====== 讀取 config.ini (修正版，兼容 .py 和 .exe) ======

# 判斷程式是以 .py 執行還是以打包後的 .exe 執行
if getattr(sys, 'frozen', False):
    # 如果是打包後的 .exe
    application_path = os.path.dirname(sys.executable)
else:
    # 如果是 .py 檔
    application_path = os.path.dirname(__file__)

# 將 config.ini 的路徑設定為相對於程式執行路徑
config_file_path = os.path.join(application_path, 'config.ini')

# (你原有的 load_config 函數定義保持不變)
def load_config():
    # ... (此函數內容不變) ...
    config = configparser.ConfigParser()
    try:
        config.read(config_file_path, encoding='utf-8')

        # SETTINGS 區段
        driver_path = config['SETTINGS']['driver_path']
        if not os.path.exists(driver_path):
             raise FileNotFoundError(f"設定的 driver_path '{driver_path}' 不存在。請檢查路徑是否正確。")
        window_width = int(config['SETTINGS']['window_width'])
        window_height = int(config['SETTINGS']['window_height'])
        enable_screenshots = config['SETTINGS']['enable_screenshots']

        # LOGIN_SETTINGS 區段 (優先從 LOGIN_SETTINGS 讀取，如果不存在則從 SETTINGS 讀取，處理帶註釋的值)
        fast_login_mode = config['LOGIN_SETTINGS'].get('fast_login_mode', config['SETTINGS'].get('fast_login_mode', 'false')).split(';')[0].strip()
        skip_login_screenshots = config['LOGIN_SETTINGS'].get('skip_login_screenshots', config['SETTINGS'].get('skip_login_screenshots', 'false')).split(';')[0].strip()
        disable_login_anomaly_checks = config['LOGIN_SETTINGS'].get('disable_login_anomaly_checks', config['SETTINGS'].get('disable_login_anomaly_checks', 'false')).split(';')[0].strip()
        fast_sequential_input = config['LOGIN_SETTINGS'].get('fast_sequential_input', config['SETTINGS'].get('fast_sequential_input', 'false')).split(';')[0].strip()
        ultra_fast_login = config['LOGIN_SETTINGS'].get('ultra_fast_login', 'false').split(';')[0].strip()

        # LOGIN_SETTINGS 時間設定 (處理帶註釋的值)
        login_page_wait = float(config['LOGIN_SETTINGS'].get('login_page_wait', '1.0').split(';')[0].strip())
        account_input_interval = float(config['LOGIN_SETTINGS'].get('account_input_interval', '0.1').split(';')[0].strip())
        account_input_interval_normal = float(config['LOGIN_SETTINGS'].get('account_input_interval_normal', '0.2').split(';')[0].strip())
        password_wait_time = float(config['LOGIN_SETTINGS'].get('password_wait_time', '0.2').split(';')[0].strip())
        password_wait_time_normal = float(config['LOGIN_SETTINGS'].get('password_wait_time_normal', '0.5').split(';')[0].strip())
        login_click_interval = float(config['LOGIN_SETTINGS'].get('login_click_interval', '0.2').split(';')[0].strip())
        login_transition_wait = float(config['LOGIN_SETTINGS'].get('login_transition_wait', '0.3').split(';')[0].strip())
        login_element_timeout = int(config['LOGIN_SETTINGS'].get('login_element_timeout', '1').split(';')[0].strip())
        login_retry_count = int(config['LOGIN_SETTINGS'].get('login_retry_count', '2').split(';')[0].strip())

        # CREDENTIALS 區段
        NAME = config['CREDENTIALS']['username']
        PASSWORD = config['CREDENTIALS']['password']
        PASSWORD2 = config['CREDENTIALS']['password2']

        # http 區段
        url = config['http']['host1']

        # SCHEDULE 區段
        scheduled_hours_str = config['SCHEDULE']['hours']
        scheduled_hours = [int(h.strip()) for h in scheduled_hours_str.split(',') if h.strip()]
        if not all(isinstance(h, int) and 0 <= h <= 23 for h in scheduled_hours) or not scheduled_hours:
             raise ValueError("排程小時數必須是 0 到 23 之間的整數且列表不為空。")

        # waittime 區段
        t = int(config['waittime']['sec'])

        # LINK API 設定
        enable_line_notification = config['LINK_API']['enable_line_notification']
        wsdl_url = config['LINK_API']['wsdl_url']
        system_code = config['LINK_API']['system_code']
        sub_cate_no = config['LINK_API']['sub_cate_no']
        image_path = config['LINK_API']['image_path']
        fixed_append_info = config['LINK_API']['fixed_append_info']

        # AUTOMAIL API 設定
        enable_automail = config['AUTOMAIL_API']['enable_automail']
        api_url = config['AUTOMAIL_API']['api_url']
        mail_to = config['AUTOMAIL_API']['mail_to']
        mail_cc = config['AUTOMAIL_API']['mail_cc']
        subject = config['AUTOMAIL_API']['subject']
        sys_name = config['AUTOMAIL_API']['sys_name']
        usr_name = config['AUTOMAIL_API']['usr_name']
        verify_ssl = config['AUTOMAIL_API']['verify_ssl']

        # TIMEOUTS 區段 (處理行內註解，支援浮點數)
        element_timeout_long = int(float(config['TIMEOUTS']['element_timeout_long'].split(';')[0].strip()))
        element_timeout_long_site = int(float(config['TIMEOUTS']['element_timeout_long_site'].split(';')[0].strip()))
        element_timeout_medium = int(float(config['TIMEOUTS']['element_timeout_medium'].split(';')[0].strip()))
        element_timeout_short = int(float(config['TIMEOUTS']['element_timeout_short'].split(';')[0].strip()))
        initial_load_pause = int(float(config['TIMEOUTS']['initial_load_pause'].split(';')[0].strip()))
        page_transition_pause = int(float(config['TIMEOUTS']['page_transition_pause'].split(';')[0].strip()))
        action_pause = float(config['TIMEOUTS']['action_pause'].split(';')[0].strip())  # 保持浮點數
        reloading_pause = int(float(config['TIMEOUTS']['reloading_pause'].split(';')[0].strip()))
        error_recovery_pause = int(float(config['TIMEOUTS']['error_recovery_pause'].split(';')[0].strip()))

        # 異常檢測設定 (處理行內註解)
        loading_timeout_sec = int(config['ANOMALY_DETECTION']['loading_timeout_sec'].split(';')[0].strip())
        first_loading_timeout_sec = int(config['ANOMALY_DETECTION']['first_loading_timeout_sec'].split(';')[0].strip())
        page_response_timeout_sec = int(config['ANOMALY_DETECTION']['page_response_timeout_sec'].split(';')[0].strip())
        evacuation_button_revert_timeout_sec = int(config['ANOMALY_DETECTION']['evacuation_button_revert_timeout_sec'].split(';')[0].strip())
        employee_status_revert_timeout_sec = int(config['ANOMALY_DETECTION']['employee_status_revert_timeout_sec'].split(';')[0].strip())

        return {
            'driver_path': driver_path, 'window_width': window_width, 'window_height': window_height,
            'url': url, 'name': NAME, 'password': PASSWORD, 'password2': PASSWORD2, 'wait_time': t,
            'scheduled_hours': sorted(list(set(scheduled_hours))), 'enable_screenshots': enable_screenshots,
            'enable_line_notification': enable_line_notification, 'wsdl_url': wsdl_url,
            'system_code': system_code, 'sub_cate_no': sub_cate_no, 'image_path': image_path,
            'fixed_append_info': fixed_append_info, 'enable_automail': enable_automail,
            'api_url': api_url, 'mail_to': mail_to, 'mail_cc': mail_cc, 'subject': subject,
            'sys_name': sys_name, 'usr_name': usr_name, 'verify_ssl': verify_ssl,
            'element_timeout_long_site': element_timeout_long_site,
            'element_timeout_long': element_timeout_long, 'element_timeout_medium': element_timeout_medium,
            'element_timeout_short': element_timeout_short, 'initial_load_pause': initial_load_pause,
            'page_transition_pause': page_transition_pause, 'action_pause': action_pause,
            'reloading_pause': reloading_pause, 'error_recovery_pause': error_recovery_pause,
            'loading_timeout_sec': loading_timeout_sec, 'first_loading_timeout_sec': first_loading_timeout_sec,
            'page_response_timeout_sec': page_response_timeout_sec,
            'evacuation_button_revert_timeout_sec': evacuation_button_revert_timeout_sec,
            'employee_status_revert_timeout_sec': employee_status_revert_timeout_sec,
            'fast_login_mode': fast_login_mode, 'skip_login_screenshots': skip_login_screenshots,
            'disable_login_anomaly_checks': disable_login_anomaly_checks, 'fast_sequential_input': fast_sequential_input,
            'ultra_fast_login': ultra_fast_login, 'login_page_wait': login_page_wait,
            'account_input_interval': account_input_interval, 'account_input_interval_normal': account_input_interval_normal,
            'password_wait_time': password_wait_time, 'password_wait_time_normal': password_wait_time_normal,
            'login_click_interval': login_click_interval, 'login_transition_wait': login_transition_wait,
            'login_element_timeout': login_element_timeout, 'login_retry_count': login_retry_count
        }
    except Exception as e:
        raise e

# ====== 必要頁面監控文字 ======
REQUIRED_TEXTS = ["戰情報表", "刷卡數據更新", "ERC查看該廠疏散", "開始疏散"]
START_TEXT = "開始疏散" # 用於判斷是否觸發ERC頁面檢查
FORBIDDEN_TEXT = "結束疏散" # 用於判斷是否觸發ERC頁面檢查 (以及事後復歸檢查)
START_TEXT = "開始疏散"
FORBIDDEN_TEXT = "結束疏散"

# ====== 執行狀態與停止旗標管理類別 ======
class AutomationStatus:
    # ... (此類內容不變) ...
    def __init__(self):
        self._stop_requested = threading.Event()
        self._is_running_lock = threading.Lock()
        self._running_thread_count = 0
    def request_stop(self): self._stop_requested.set()
    def is_stop_requested(self): return self._stop_requested.is_set()
    def wait_for_stop(self, timeout=None): return self._stop_requested.wait(timeout)
    def reset_stop(self): self._stop_requested.clear()
    def start_cycle(self):
        with self._is_running_lock: self._running_thread_count += 1
    def end_cycle(self):
        with self._is_running_lock: self._running_thread_count -= 1
    def is_any_running(self):
        with self._is_running_lock: return self._running_thread_count > 0

# ====== 可中斷的等待函數 ======
def interruptible_sleep(duration_seconds, status: AutomationStatus, q: queue.Queue, reason="等待指定時間"):
    # ... (此函數內容不變) ...
    if duration_seconds <= 0: return True
    q.put({'type': 'countdown_start', 'reason': reason, 'duration': duration_seconds, 'start_time': time.time()})
    stopped = status.wait_for_stop(timeout=duration_seconds)
    q.put({'type': 'countdown_stop'})
    return not stopped

# ====== LINK API 通知管理器 ======
class LinkNotificationManager:
    """LINK API 通知管理器"""

    def __init__(self, config_data):
        self.config = config_data
        self.client = None
        self.anomaly_sites = []  # 儲存異常站點資訊

    def initialize_client(self):
        """初始化 SOAP 客戶端"""
        try:
            if self.config['enable_line_notification'].lower() == 'true':
                wsdl_url = self.config.get('wsdl_url')
                self.client = zeep.Client(wsdl=wsdl_url)
                return True
            return False
        except Exception as e:
            print(f"初始化 LINK API 客戶端失敗: {e}")
            return False

    def add_anomaly_site(self, site_name, condition):
        """添加異常站點資訊"""
        self.anomaly_sites.append(f"{site_name}+{condition}")

    def clear_anomaly_sites(self):
        """清空異常站點列表"""
        self.anomaly_sites = []

    def send_notification(self):
        """發送 LINK 通知"""
        if not self.client or not self.anomaly_sites:
            return False

        try:
            # 準備圖片
            image_path = self.config.get('image_path', 'pic_output/pic.jpg')
            base64_image_content = self._prepare_image(image_path)

            # 生成事件內容
            anomaly_conditions = ",".join(self.anomaly_sites)
            event_content = f"疏散平台系統發生異常\n{anomaly_conditions}\n請盡速排除，Site ESH請整備紙本替代方案"

            # 準備 JSON 請求內容
            payload = {
                "FunctionName": "PublishEvent",
                "SystemCode": self.config['system_code'],
                "SubCateNo": self.config['sub_cate_no'],
                "EventContent": event_content,
                "FixedAppendInfo": self.config.get('fixed_append_info', ''),
                "HasAttachedImage": base64_image_content is not None,
                "AttachedImages": [{"Base64ImageContent": base64_image_content}] if base64_image_content else []
            }

            # 轉換成 JSON 字符串
            json_request_content = json.dumps(payload)

            # 呼叫SOAP函數
            response = self.client.service.PublishEvent(json_request_content)

            # 解析回應
            response_dict = json.loads(response)
            root_event_no = response_dict.get('Result', {}).get('RootEventNo', None)
            return_msg = response_dict.get('ReturnMsg', '')

            print(f"LINK 通知發送成功 - Event No: {root_event_no}, Message: {return_msg}")
            return True

        except Exception as e:
            print(f"發送 LINK 通知失敗: {e}")
            return False

    def _prepare_image(self, image_path):
        """準備圖片的 Base64 編碼"""
        try:
            if os.path.exists(image_path):
                with open(image_path, "rb") as image_file:
                    return base64.b64encode(image_file.read()).decode("utf-8")
            else:
                print(f"找不到圖檔: {image_path}")
                return None
        except Exception as e:
            print(f"處理圖片失敗: {e}")
            return None


# ====== AUTOMAIL API 通知管理器 ======
class AutomailNotificationManager:
    """AUTOMAIL API 通知管理器"""

    def __init__(self, config_data):
        self.config = config_data
        self.anomaly_sites = []  # 儲存異常站點資訊

    def is_enabled(self):
        """檢查是否啟用 AUTOMAIL 通知"""
        return self.config['enable_automail'].lower() == 'true'

    def add_anomaly_site(self, site_name, condition):
        """添加異常站點資訊"""
        self.anomaly_sites.append(f"{site_name}: {condition}")

    def clear_anomaly_sites(self):
        """清空異常站點列表"""
        self.anomaly_sites = []

    def send_notification(self):
        """發送 AUTOMAIL 通知"""
        if not self.is_enabled() or not self.anomaly_sites:
            return False

        try:
            # 生成 HTML 郵件內容
            anomaly_list = "<br>".join([f"• {site}" for site in self.anomaly_sites])
            full_html = f"""
            <html>
            <head>
                <meta charset="UTF-8">
                <title>疏散平台異常通知</title>
            </head>
            <body style="font-family: Arial, sans-serif; margin: 20px;">
                <h2 style="color: #d32f2f;">🚨 疏散平台系統異常通知</h2>
                <p>系統檢測到以下地點存在疏散異常：</p>
                <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 10px 0;">
                    {anomaly_list}
                </div>
                <p><strong>請盡速排除異常，Site ESH請整備紙本替代方案。</strong></p>
                <hr style="margin: 20px 0;">
                <p style="color: #666; font-size: 12px;">
                    此郵件由疏散平台自動化監控系統自動發送<br>
                    發送時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
                </p>
            </body>
            </html>
            """

            # 準備請求的JSON數據
            payload = {
                'body': full_html,
                'mail_to': self.config.get('mail_to', '<EMAIL>'),
                'mail_cc': self.config.get('mail_cc', '<EMAIL>'),
                'subject': self.config['subject'],
                'Sys': self.config['sys_name'],
                'Usr': self.config['usr_name']
            }

            # 設定HTTP Header
            headers = {
                'Content-Type': 'application/json; charset=utf-8',
                'User-Agent': 'Python/Requests'
            }

            # 發送HTTP POST請求
            api_url = self.config.get('api_url', 'https://auhqesh.corpnet.auo.com/Mail/IDS_MailSent_JSON')
            verify_ssl = self.config['verify_ssl'].lower() == 'true'

            response = requests.post(api_url, json=payload, headers=headers, verify=verify_ssl)

            # 解析回應
            if response.status_code == 200:
                print("AUTOMAIL 通知發送成功")
                return True
            else:
                print(f"AUTOMAIL 通知發送失敗 - HTTP錯誤碼: {response.status_code}")
                print(f"錯誤訊息: {response.text}")
                return False

        except requests.exceptions.RequestException as e:
            print(f"發送 AUTOMAIL 通知失敗: {e}")
            return False
        except Exception as e:
            print(f"AUTOMAIL 通知處理失敗: {e}")
            return False

# ====== Selenium 操作封裝類別 ======
class SeleniumAutomation:
    def __init__(self, status: AutomationStatus, q: queue.Queue, config_data):
        self.status = status
        self.q = q
        self.config = config_data
        self.driver = None

        # 截圖功能相關屬性
        self.screenshot_counter = 0
        self.current_session_folder = None
        # 確保根截圖資料夾存在
        self._ensure_screenshot_folder()

        # 新增：追蹤已執行「待確認人」測試的主要地點，確保每個主要地點只執行一次
        self.wait_confirm_tested_main_sites = set()

        # 新增：日誌檔案功能
        self.log_file_path = None
        self._setup_log_file()

        # 新增：LINK 通知管理器
        self.link_notification = LinkNotificationManager(config_data)

        # 新增：時間記錄系統
        self.timing_records = {}
        self.current_stage_start = None
        self.login_start_time = None
        self.link_notification.initialize_client()

        # 新增：圖片合併器和關鍵截圖追蹤
        self.image_merger = ImageMerger()
        self.critical_screenshots = []  # 儲存關鍵截圖路徑

        # 新增：AUTOMAIL 通知管理器
        self.automail_notification = AutomailNotificationManager(config_data)

        # 新增：登入處理器 (將在 WebDriver 初始化後設定)
        self.login_handler = None

    class Locators:
        # 登入頁面
        LOGIN_ACCOUNT_INPUT = (By.ID, 'txtAccount_E')
        LOGIN_IDNO_INPUT = (By.ID, 'txtIDNO')
        LOGIN_NEXT_BUTTON = (By.ID, 'btnNext_E')
        LOGIN_PASSWORD_INPUT = (By.ID, 'txtPassword_E')
        LOGIN_BUTTON = (By.ID, 'btnLogin_E')

        # 主頁面
        SITE_BOX_ITEM = (By.CLASS_NAME, 'site-box-item')
        TAB_ELEMENT_XPATH = "//div[@role='tab']//span[contains(@class,'van-tab__text') and normalize-space()='{}']/parent::div"
        ACTIVE_TAB_PANEL = (By.XPATH, "//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]")
        SITE_BUTTON_TEXT = "//span[contains(@class, 'van-button__text') and normalize-space()='{}']"
        SITE_BUTTON_ANCESTOR = "/ancestor::button"
        BADGE_ELEMENT = (By.CLASS_NAME, 'badge')

        # 詳細頁面
        START_EVAC_BUTTON = (By.XPATH, "//button[contains(., '開始疏散')]")
        END_EVAC_BUTTON = (By.XPATH, "//button[contains(., '結束疏散')]")
        ERC_VIEW_BUTTON = (By.XPATH, "//button[contains(., 'ERC查看該廠疏散')]")
        GENERIC_BACK_BUTTON = (By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]")
        BACK_TO_DETAIL_BUTTON = (By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]//span[contains(@class, 'van-nav-bar__text') and normalize-space()='疏散總表']")

        # ERC頁面
        ERC_TAB_NAV_LINE = (By.CLASS_NAME, 'van-tabs__nav--line')
        # ## DEBUG修復 ## 將 ERC_TAB_TEXT 修正為一個包含 By.XPATH 的元組 (tuple)
        ERC_TAB_TEXT = (By.XPATH, "//div[contains(@class, 'van-tabs__nav--line')]//span[contains(@class,'van-tab__text')]")
        ERC_DASHBOARD_SURE_COUNT = (By.CSS_SELECTOR, '.dashboard-box .sure-count')
        ERC_DASHBOARD_WAIT_COUNT = (By.CSS_SELECTOR, '.dashboard-box .wait-count')
        ERC_SITE_BOX_ITEM = (By.CSS_SELECTOR, '.site-box-item')

        # 地點名稱和角標
        SITE_NAME = (By.CSS_SELECTOR, '.van-button__text')
        SITE_BADGE = (By.CSS_SELECTOR, '.badge')

        # 待確認人處理
        WAIT_CONFIRM_CLICKABLE_XPATH = ("//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]"
                                        "//div[contains(@class, 'count-title') and contains(., '待確認人：') and .//i[contains(@class, 'van-icon-arrow')]]")
        WAIT_COUNT_SPAN = (By.CLASS_NAME, 'wait-count')
        UNEVACUATED_BUTTON = (By.XPATH, "//button[.//span[text()='未疏散']]")
        EVACUATED_BUTTON = (By.XPATH, "//button[.//span[text()='已疏散']]")
        ERC_BACK_BUTTON_FROM_DETAIL = (By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]//span[text()='疏散詳情']")

        # 員工疏散狀態測試
        EMPLOYEE_UNEVACUATED_BUTTON = (By.XPATH, ".//button[.//span[text()='未疏散']]")
        EMPLOYEE_EVACUATED_BUTTON = (By.XPATH, ".//button[.//span[text()='已疏散']]")
        EMPLOYEE_NAME_STRATEGIES = [".//span[@class='emp-name']", ".//*[contains(@class, 'emp-name')]", ".//*[contains(@class, 'name')]", ".//span[1]"]

        # 新增按鈕相關 (根據實際網頁結構)
        ADD_BUTTON = (By.XPATH, "//button[.//span[contains(text(), '新增')] or .//i[contains(@class, 'van-icon-add')]]")
        ADD_MODAL_WINDOW = (By.XPATH, "//div[contains(@class, 'van-popup') and not(contains(@style, 'display: none'))]")

    def _check_stop(self):
        if self.status.is_stop_requested():
            raise InterruptedError("停止請求已接收，中斷當前自動化操作。")

    # ============================================
    # ====== 新增: 載入動畫監控方法 ======
    # ============================================
    def monitor_loading_animation(self, timeout_sec=15):
        """
        使用非同步JS在瀏覽器中監控各種載入動畫，並回傳其顯示時長。
        支援多種載入動畫類型：Vant UI、通用載入動畫、文字載入提示等。
        """
        self._check_stop()
        self.q.put({'type': 'log', 'message': f"⏱️ 開始監控載入動畫 (最長等待 {timeout_sec} 秒)...", 'level': 'INFO'})

        # 要注入瀏覽器的 JavaScript 程式碼
        js_script = """
            const callback = arguments[arguments.length - 1];
            const timeout = arguments[0];

            // 多種載入動畫選擇器
            const targetSelectors = [
                '.van-popup.van-toast.van-toast--loading',  // Vant UI 載入動畫
                '.van-loading',                             // Vant 載入組件
                '.loading',                                 // 通用載入類
                '.spinner',                                 // 旋轉載入
                '.loader',                                  // 載入器
                '[class*="loading"]',                       // 包含loading的類名
                '[class*="spinner"]',                       // 包含spinner的類名
                '[class*="loader"]'                         // 包含loader的類名
            ];

            // 載入文字關鍵字
            const loadingTexts = ['加載中', '載入中', '請稍候', 'Loading', 'LOADING', 'loading'];

            let startTime = 0;
            let observer;
            let timeoutId;
            let foundElement = null;
            let detectionMethod = '';

            // 尋找目標節點
            for (const selector of targetSelectors) {
                const element = document.querySelector(selector);
                if (element) {
                    foundElement = element;
                    detectionMethod = `CSS選擇器: ${selector}`;
                    break;
                }
            }

            // 如果沒找到CSS選擇器，嘗試通過文字內容查找
            if (!foundElement) {
                const allElements = document.querySelectorAll('*');
                for (const element of allElements) {
                    const text = element.textContent || element.innerText || '';
                    if (loadingTexts.some(loadingText => text.includes(loadingText))) {
                        foundElement = element;
                        detectionMethod = `文字內容: "${text.trim()}"`;
                        break;
                    }
                }
            }

            if (!foundElement) {
                // 如果頁面上一開始就沒有這個元素，直接回傳'NOT_FOUND'
                callback('NOT_FOUND');
                return;
            }

            // 記錄找到的元素信息
            console.log('找到載入動畫元素:', detectionMethod, foundElement);

            // 清理資源並回傳結果
            const cleanupAndCallback = (result) => {
                if (observer) {
                    observer.disconnect();
                }
                if (timeoutId) {
                    clearTimeout(timeoutId);
                }
                callback(result);
            };

            // 檢查元素是否當前可見
            const isElementVisible = (element) => {
                if (!element) return false;
                const style = window.getComputedStyle(element);
                return style.display !== 'none' &&
                       style.visibility !== 'hidden' &&
                       style.opacity !== '0' &&
                       element.offsetWidth > 0 &&
                       element.offsetHeight > 0;
            };

            // 檢查元素是否包含載入文字
            const hasLoadingText = (element) => {
                const text = element.textContent || element.innerText || '';
                return loadingTexts.some(loadingText => text.includes(loadingText));
            };

            // 如果元素當前可見，開始計時
            if (isElementVisible(foundElement) || hasLoadingText(foundElement)) {
                startTime = performance.now();
                console.log('載入動畫開始計時:', detectionMethod);
            }

            // 建立一個監視器實例
            observer = new MutationObserver((mutationsList) => {
                for (const mutation of mutationsList) {
                    const target = mutation.target;
                    const isVisible = isElementVisible(target) || hasLoadingText(target);

                    // 如果變為可見且尚未開始計時
                    if (isVisible && startTime === 0) {
                        startTime = performance.now();
                        console.log('載入動畫開始 (通過監視器):', detectionMethod);
                    }
                    // 如果變為隱藏且已經開始計時
                    else if (!isVisible && startTime > 0) {
                        const endTime = performance.now();
                        const duration = endTime - startTime;
                        console.log('載入動畫結束:', detectionMethod, '耗時:', duration + 'ms');
                        cleanupAndCallback(duration); // 回傳持續時間(毫秒)
                        return;
                    }
                }
            });

            // 開始監視目標節點的變化
            observer.observe(foundElement, {
                attributes: true,
                childList: true,
                subtree: true,
                attributeFilter: ['style', 'class']
            });

            // 同時監視整個文檔的變化（針對動態插入的載入元素）
            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // 設置一個超時，防止無限等待
            timeoutId = setTimeout(() => {
                console.log('載入動畫監控超時');
                cleanupAndCallback('TIMEOUT');
            }, timeout);

            // 設置一個定期檢查，以防錯過快速變化
            const checkInterval = setInterval(() => {
                if (!isElementVisible(foundElement) && !hasLoadingText(foundElement) && startTime > 0) {
                    const endTime = performance.now();
                    const duration = endTime - startTime;
                    console.log('載入動畫結束 (定期檢查):', detectionMethod, '耗時:', duration + 'ms');
                    clearInterval(checkInterval);
                    cleanupAndCallback(duration);
                }
            }, 100); // 每100ms檢查一次
        """

        try:
            # 執行異步腳本
            duration_ms = self.driver.execute_async_script(js_script, timeout_sec * 1000)

            # 處理回傳結果
            if isinstance(duration_ms, (int, float)):
                duration_s = duration_ms / 1000
                self.q.put({'type': 'log', 'message': f"  ✅ 載入動畫完成，耗時: {duration_s:.3f} 秒。", 'level': 'INFO'})

                # 檢查是否為初次載入且超過設定的異常檢測時間
                if timeout_sec == self.config['first_loading_timeout_sec'] and duration_s > self.config['loading_timeout_sec']:
                    self.q.put({'type': 'log', 'message': f"  ⚠️ 初次載入動畫時間異常：{duration_s:.3f} 秒 (超過設定值 {self.config['loading_timeout_sec']} 秒)", 'level': 'WARNING'})
                    # 可以在這裡添加異常通知邏輯

            elif duration_ms == 'TIMEOUT':
                self.q.put({'type': 'log', 'message': f"  ⚠️ 監控載入動畫超時 (超過 {timeout_sec} 秒)。", 'level': 'WARNING'})
                # 超時時嘗試手動檢查頁面是否還有載入元素
                self._manual_check_loading_elements()
            elif duration_ms == 'NOT_FOUND':
                self.q.put({'type': 'log', 'message': "  ℹ️ 未在頁面上找到載入動畫元素，進行手動檢查...", 'level': 'INFO'})
                # 進行手動檢查
                self._manual_check_loading_elements()
            else:
                self.q.put({'type': 'log', 'message': f"  ❓ 監控載入動畫回傳未知結果: {duration_ms}", 'level': 'WARNING'})

        except InterruptedError:
            raise
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"  ❌ 監控載入動畫時發生錯誤: {e}", 'level': 'ERROR'})
            # 發生錯誤時也進行手動檢查
            self._manual_check_loading_elements()

    def _extract_badge_numbers_from_source(self, site_name, html_content):
        """
        從頁面源碼中提取角標數字，作為CSS選擇器的備用方案
        """
        try:
            import re

            # 從site_name中提取實際的角標數字
            # 例如：華亞 (710) -> 710, 龍科 (2158) -> 2158
            badge_match = re.search(r'\((\d+)\)', site_name)
            if badge_match:
                total_count = badge_match.group(1)
                self.q.put({'type': 'log', 'message': f"    從地點名稱中提取到角標數字: {total_count}", 'level': 'DEBUG'})

                # 嘗試從HTML中找到應確認和待確認的具體數字
                # 搜索可能的模式
                patterns = [
                    r'應確認[：:]\s*(\d+)',
                    r'sure-count["\']>\s*(\d+)',
                    r'確認.*?(\d+)',
                    r'待確認[：:]\s*(\d+)',
                    r'wait-count["\']>\s*(\d+)',
                    r'待確認.*?(\d+)'
                ]

                sure_count = 'N/A'
                wait_count = 'N/A'

                for pattern in patterns:
                    matches = re.findall(pattern, html_content, re.IGNORECASE)
                    if matches:
                        if '應確認' in pattern or 'sure-count' in pattern:
                            sure_count = matches[0]
                        elif '待確認' in pattern or 'wait-count' in pattern:
                            wait_count = matches[0]

                # 如果仍然找不到，使用總數作為待確認人數（這是常見的情況）
                if sure_count == 'N/A' and wait_count == 'N/A':
                    sure_count = '0'  # 通常應確認人數為0
                    wait_count = total_count  # 待確認人數等於總數
                    self.q.put({'type': 'log', 'message': f"    使用預設邏輯 - 應確認: {sure_count}, 待確認: {wait_count}", 'level': 'DEBUG'})

                return sure_count, wait_count
            else:
                self.q.put({'type': 'log', 'message': f"    無法從地點名稱中提取角標數字: {site_name}", 'level': 'WARNING'})
                return 'N/A', 'N/A'

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"    從頁面源碼提取角標數字時發生錯誤: {e}", 'level': 'ERROR'})
            return 'N/A', 'N/A'

    def _process_erc_dept_full(self, dept_tab_name, parent_site_name, parent_tab_name):
        """
        完整處理ERC部門（包含截圖和測試）- 用於第一個部門
        """
        try:
            self.q.put({'type': 'log', 'message': f"\n    --- 處理ERC部門標籤「{dept_tab_name}」下的數據（完整模式） ---", 'level': 'INFO'})

            # 重新解析當前頁面源，以獲取活動Tab的最新內容
            html_content_current_tab = self.driver.page_source
            soup_current_tab = BeautifulSoup(html_content_current_tab, 'html.parser')

            # 確保找到的是當前活動的 ERC 部門面板
            active_tab_panel = soup_current_tab.select_one('div.van-tab__panel:not([style*="display: none"])')

            if active_tab_panel:
                self.q.put({'type': 'log', 'message': f"      提取ERC部門 [{dept_tab_name}] 下的地點人數數據...", 'level': 'INFO'})

                # 提取地點數據
                site_items = active_tab_panel.select('.site-box-item')
                small_site_for_testing = None

                if site_items:
                    for item in site_items:
                        self._check_stop()

                        # 提取地點名稱和人數
                        site_name_elem = item.select_one('.van-button__text')
                        badge_elem = item.select_one('.badge')

                        if site_name_elem and badge_elem:
                            site_name = site_name_elem.get_text(strip=True)
                            badge_value_str = badge_elem.get_text(strip=True)

                            try:
                                people_count = int(badge_value_str)
                                combined_name = f"{site_name} ({people_count})"
                                result_str = f"✓ 已檢查"

                                self.q.put({'type': 'log', 'message': f"        - 地點: {combined_name}", 'level': 'INFO'})

                                # 發送詳細結果
                                self.q.put({
                                    'type': 'erc_dept_site_item_result',
                                    'main_tab': parent_tab_name,
                                    'main_site_name': parent_site_name.split(' (')[0],
                                    'main_site_total_people': parent_site_name.split(' (')[1].strip(')'),
                                    'erc_dept_tab': dept_tab_name,
                                    'erc_dept_site_item': site_name,
                                    'erc_dept_site_people': badge_value_str,
                                    'detailed_check_result': result_str
                                })

                                # 尋找適合測試的小地點
                                if people_count < 8 and small_site_for_testing is None:
                                    small_site_for_testing = (site_name, people_count)

                            except ValueError:
                                self.q.put({'type': 'log', 'message': f"        - 地點: {site_name} (人數格式錯誤: {badge_value_str})", 'level': 'WARNING'})

                # 執行疏散狀態測試
                if small_site_for_testing:
                    main_site_key = parent_site_name.split(' (')[0]
                    skip_after_first = self.config.get('skip_evacuation_testing_after_first', False)

                    if main_site_key not in self.wait_confirm_tested_main_sites:
                        if skip_after_first and len(self.wait_confirm_tested_main_sites) > 0:
                            self.q.put({'type': 'log', 'message': f"        快速模式：已執行過疏散狀態測試，跳過主要地點「{main_site_key}」的子地點「{small_site_for_testing[0]}」以加快速度", 'level': 'INFO'})
                        else:
                            self.wait_confirm_tested_main_sites.add(main_site_key)
                            self.q.put({'type': 'log', 'message': f"        首次對主要地點「{main_site_key}」進行疏散狀態測試（子地點：{small_site_for_testing[0]}）", 'level': 'INFO'})
                            self._test_evacuation_status_for_small_site(small_site_for_testing[0], small_site_for_testing[1], dept_tab_name)
                    else:
                        self.q.put({'type': 'log', 'message': f"        主要地點「{main_site_key}」已執行過疏散狀態測試，跳過子地點「{small_site_for_testing[0]}」", 'level': 'INFO'})

                # 執行人員疏散狀態測試
                main_site_key = parent_site_name.split(' (')[0]
                skip_after_first = self.config.get('skip_evacuation_testing_after_first', False)

                if main_site_key not in self.wait_confirm_tested_main_sites and not skip_after_first:
                    self.q.put({'type': 'log', 'message': f"      開始執行ERC部門 [{dept_tab_name}] 的人員疏散狀態測試...", 'level': 'INFO'})
                    self._process_unconfirmed_persons_details(dept_tab_name)
                elif skip_after_first and len(self.wait_confirm_tested_main_sites) > 0:
                    self.q.put({'type': 'log', 'message': f"      快速模式：已執行過疏散狀態測試，跳過ERC部門 [{dept_tab_name}] 的人員疏散狀態測試以加快速度", 'level': 'INFO'})
                else:
                    self.q.put({'type': 'log', 'message': f"      主要地點「{main_site_key}」已執行過疏散狀態測試，跳過ERC部門 [{dept_tab_name}] 的人員疏散狀態測試", 'level': 'INFO'})
            else:
                self.q.put({'type': 'log', 'message': f"      ERC部門 [{dept_tab_name}] 未找到 site-box-item。", 'level': 'INFO'})

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"      處理ERC部門 [{dept_tab_name}] 時發生錯誤: {e}", 'level': 'ERROR'})

    def _process_erc_dept_fast(self, dept_tab_name, parent_site_name, parent_tab_name):
        """
        快速處理ERC部門（僅提取數據，無截圖無測試）- 用於後續部門
        """
        try:
            self.q.put({'type': 'log', 'message': f"\n    --- 快速處理ERC部門標籤「{dept_tab_name}」下的數據（僅提取數據） ---", 'level': 'INFO'})

            # 快速切換標籤（無截圖）
            if self._click_tab_fast(dept_tab_name):
                # 重新解析當前頁面源
                html_content_current_tab = self.driver.page_source
                soup_current_tab = BeautifulSoup(html_content_current_tab, 'html.parser')

                # 確保找到的是當前活動的 ERC 部門面板
                active_tab_panel = soup_current_tab.select_one('div.van-tab__panel:not([style*="display: none"])')

                if active_tab_panel:
                    self.q.put({'type': 'log', 'message': f"      提取ERC部門 [{dept_tab_name}] 下的地點人數數據...", 'level': 'INFO'})

                    # 提取地點數據
                    site_items = active_tab_panel.select('.site-box-item')

                    if site_items:
                        for item in site_items:
                            self._check_stop()

                            # 提取地點名稱和人數
                            site_name_elem = item.select_one('.van-button__text')
                            badge_elem = item.select_one('.badge')

                            if site_name_elem and badge_elem:
                                site_name = site_name_elem.get_text(strip=True)
                                badge_value_str = badge_elem.get_text(strip=True)

                                try:
                                    people_count = int(badge_value_str)
                                    combined_name = f"{site_name} ({people_count})"
                                    result_str = f"✓ 已檢查（快速模式）"

                                    self.q.put({'type': 'log', 'message': f"        - 地點: {combined_name}", 'level': 'INFO'})

                                    # 發送詳細結果
                                    self.q.put({
                                        'type': 'erc_dept_site_item_result',
                                        'main_tab': parent_tab_name,
                                        'main_site_name': parent_site_name.split(' (')[0],
                                        'main_site_total_people': parent_site_name.split(' (')[1].strip(')'),
                                        'erc_dept_tab': dept_tab_name,
                                        'erc_dept_site_item': site_name,
                                        'erc_dept_site_people': badge_value_str,
                                        'detailed_check_result': result_str
                                    })

                                except ValueError:
                                    self.q.put({'type': 'log', 'message': f"        - 地點: {site_name} (人數格式錯誤: {badge_value_str})", 'level': 'WARNING'})

                    # 快速模式：跳過所有測試
                    self.q.put({'type': 'log', 'message': f"      快速模式：跳過ERC部門 [{dept_tab_name}] 的所有測試以加快速度", 'level': 'INFO'})
                else:
                    self.q.put({'type': 'log', 'message': f"      ERC部門 [{dept_tab_name}] 未找到 site-box-item。", 'level': 'INFO'})
            else:
                self.q.put({'type': 'log', 'message': f"      快速切換到ERC部門標籤「{dept_tab_name}」失敗", 'level': 'WARNING'})

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"      快速處理ERC部門 [{dept_tab_name}] 時發生錯誤: {e}", 'level': 'ERROR'})

    def _process_erc_dept_ultra_fast(self, dept_tab_name, parent_site_name, parent_tab_name):
        """
        超快速處理ERC部門（最小化操作）
        """
        try:
            # 快速切換標籤（無等待）
            if self._click_tab_instant(dept_tab_name):
                # 最小等待時間
                time.sleep(0.2)
                
                # 快速提取數據
                html_content = self.driver.page_source
                soup = BeautifulSoup(html_content, 'html.parser')
                
                # 直接搜索地點數據
                site_items = soup.select('.site-box-item')
                
                for item in site_items:
                    site_name_elem = item.select_one('.van-button__text')
                    badge_elem = item.select_one('.badge')
                    
                    if site_name_elem and badge_elem:
                        site_name = site_name_elem.get_text(strip=True)
                        badge_value = badge_elem.get_text(strip=True)
                        
                        # 只記錄，不截圖
                        self.q.put({'type': 'log', 'message': f"        - 地點: {site_name} ({badge_value})", 'level': 'INFO'})
                        
                        # 發送結果
                        self.q.put({
                            'type': 'erc_dept_site_item_result',
                            'main_tab': parent_tab_name,
                            'main_site_name': parent_site_name.split(' (')[0],
                            'main_site_total_people': parent_site_name.split(' (')[1].strip(')'),
                            'erc_dept_tab': dept_tab_name,
                            'erc_dept_site_item': site_name,
                            'erc_dept_site_people': badge_value,
                            'detailed_check_result': '✓ 已檢查（超快速模式）'
                        })
                
                self.q.put({'type': 'log', 'message': f"      超快速模式：完成ERC部門 [{dept_tab_name}] 數據提取", 'level': 'INFO'})
                
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"      超快速處理ERC部門 [{dept_tab_name}] 時發生錯誤: {e}", 'level': 'ERROR'})

    def _click_tab_instant(self, tab_name):
        """
        瞬間點擊標籤（最小化等待）
        """
        try:
            # 尋找標籤元素
            tab_elements = self.driver.find_elements(*self.Locators.ERC_TAB_TEXT)
            target_tab = None

            for tab_elem in tab_elements:
                if tab_elem.text.strip() == tab_name:
                    target_tab = tab_elem
                    break

            if target_tab:
                # 瞬間點擊
                self.driver.execute_script("arguments[0].click();", target_tab)
                return True
            else:
                return False

        except Exception as e:
            return False

    def _select_first_dept_for_full_processing(self, tab_names, site_name):
        """
        智能選擇第一個部門進行完整處理
        優先選擇有較多地點的部門
        """
        # 預設優先順序
        priority_depts = ['Undefine', 'Other', 'HY_Other', 'Array', 'Cell']

        # 按優先順序選擇
        for priority_dept in priority_depts:
            if priority_dept in tab_names:
                return priority_dept

        # 如果沒有優先部門，選擇第一個
        return tab_names[0] if tab_names else None

    def _click_tab_fast(self, tab_name):
        """
        快速點擊標籤（無截圖、無等待動畫）
        """
        try:
            # 尋找標籤元素
            tab_elements = self.driver.find_elements(*self.Locators.ERC_TAB_TEXT)
            target_tab = None

            for tab_elem in tab_elements:
                if tab_elem.text.strip() == tab_name:
                    target_tab = tab_elem
                    break

            if target_tab:
                # 快速點擊
                self.driver.execute_script("arguments[0].click();", target_tab)
                time.sleep(0.5)  # 最小等待時間
                return True
            else:
                return False

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"      快速點擊標籤「{tab_name}」時發生錯誤: {e}", 'level': 'ERROR'})
            return False

    def _manual_check_loading_elements(self):
        """
        手動檢查頁面中的載入元素，作為JavaScript監控的備用方案
        """
        try:
            # 檢查頁面源碼中是否包含載入相關文字
            page_source = self.driver.page_source
            loading_texts = ['加載中', '載入中', '請稍候', 'Loading', 'LOADING', 'loading']

            found_loading_text = None
            for text in loading_texts:
                if text in page_source:
                    found_loading_text = text
                    break

            if found_loading_text:
                self.q.put({'type': 'log', 'message': f"  🔍 手動檢查發現載入文字: '{found_loading_text}'", 'level': 'INFO'})

                # 等待載入完成
                max_wait = 10  # 最多等待10秒
                start_time = time.time()

                while time.time() - start_time < max_wait:
                    time.sleep(0.5)
                    current_source = self.driver.page_source
                    if found_loading_text not in current_source:
                        elapsed = time.time() - start_time
                        self.q.put({'type': 'log', 'message': f"  ✅ 手動檢查確認載入完成，耗時: {elapsed:.3f} 秒", 'level': 'INFO'})
                        return

                self.q.put({'type': 'log', 'message': f"  ⚠️ 手動檢查載入文字仍存在，可能載入時間較長", 'level': 'WARNING'})
            else:
                # 檢查是否有載入相關的CSS類或元素
                loading_selectors = [
                    '.van-loading', '.loading', '.spinner', '.loader',
                    '[class*="loading"]', '[class*="spinner"]', '[class*="loader"]'
                ]

                found_elements = []
                for selector in loading_selectors:
                    try:
                        elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                        if elements:
                            found_elements.extend([(selector, len(elements))])
                    except:
                        continue

                if found_elements:
                    element_info = ', '.join([f"{sel}({count}個)" for sel, count in found_elements])
                    self.q.put({'type': 'log', 'message': f"  🔍 手動檢查發現載入元素: {element_info}", 'level': 'INFO'})
                else:
                    self.q.put({'type': 'log', 'message': "  ℹ️ 手動檢查未發現明顯的載入元素", 'level': 'INFO'})

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"  ❌ 手動檢查載入元素時發生錯誤: {e}", 'level': 'ERROR'})

    # ====== 異常檢測方法 ======
    def _check_login_anomalies(self, stage_description):
        """檢測登入階段異常"""
        anomalies = []

        try:
            # 檢查頁面是否無反應（維持在同一頁面）
            current_url = self.driver.current_url
            if "login" in current_url.lower() or "signin" in current_url.lower():
                # 等待一段時間看頁面是否有變化
                time.sleep(self.config['page_response_timeout_sec'])
                new_url = self.driver.current_url
                if current_url == new_url:
                    anomaly = f"{stage_description}: 網頁無反應(維持在同一頁面)"
                    anomalies.append(anomaly)
                    self._log_message(f"異常檢測: {anomaly}", 'WARNING')

            # 檢查是否有登入錯誤訊息
            try:
                error_elements = self.driver.find_elements(By.XPATH, "//*[contains(text(), '錯誤') or contains(text(), 'error') or contains(text(), 'Error') or contains(text(), '失敗')]")
                if error_elements:
                    anomaly = f"{stage_description}: 登入訊息錯誤"
                    anomalies.append(anomaly)
                    self._log_message(f"異常檢測: {anomaly}", 'WARNING')
            except:
                pass

        except Exception as e:
            self._log_message(f"登入異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_page_loading_anomalies(self, page_description):
        """檢測頁面載入異常"""
        anomalies = []

        try:
            # 檢查空白畫面/黑屏
            body_element = self.driver.find_element(By.TAG_NAME, 'body')
            body_text = body_element.text.strip()

            if not body_text or len(body_text) < 10:
                anomaly = f"{page_description}: 空白畫面/黑屏"
                anomalies.append(anomaly)
                self._log_message(f"異常檢測: {anomaly}", 'WARNING')

            # 檢查加載中狀態是否超過設定時間
            loading_timeout = self.config['loading_timeout_sec']
            loading_indicators = [
                "loading", "載入中", "加載中", "請稍候", "Loading", "LOADING",
                "van-loading", "spinner", "loader"
            ]

            start_time = time.time()
            while time.time() - start_time < loading_timeout + 1:  # 多等1秒確認
                page_source = self.driver.page_source.lower()
                has_loading = any(indicator.lower() in page_source for indicator in loading_indicators)

                if has_loading:
                    if time.time() - start_time >= loading_timeout:
                        anomaly = f"{page_description}: 加載中(畫面出現超過{loading_timeout}sec)"
                        anomalies.append(anomaly)
                        self._log_message(f"異常檢測: {anomaly}", 'WARNING')
                        break
                    time.sleep(0.5)
                else:
                    break

        except Exception as e:
            self._log_message(f"頁面載入異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_site_data_anomalies(self, tab_name, sites_data):
        """檢測站點數據異常"""
        anomalies = []

        try:
            # 檢查Site缺少或重複
            expected_sites = SITE_DATA.get(tab_name, [])
            actual_sites = [site_info.split(' (')[0] for site_info in sites_data.keys()]

            # 檢查缺少的站點
            missing_sites = set(expected_sites) - set(actual_sites)
            if missing_sites:
                anomaly = f"{tab_name}: Site缺少 - {', '.join(missing_sites)}"
                anomalies.append(anomaly)
                self._log_message(f"異常檢測: {anomaly}", 'WARNING')

            # 檢查重複的站點
            duplicate_sites = [site for site in actual_sites if actual_sites.count(site) > 1]
            if duplicate_sites:
                anomaly = f"{tab_name}: Site重複 - {', '.join(set(duplicate_sites))}"
                anomalies.append(anomaly)
                self._log_message(f"異常檢測: {anomaly}", 'WARNING')

            # 檢查Site角標數字為0 (重要異常，需要截圖)
            for site_name, count in sites_data.items():
                if count == 0:
                    # 提取純站點名稱（去除人數部分）
                    clean_site_name = site_name.split(' (')[0] if ' (' in site_name else site_name
                    anomaly = f"{tab_name}: Site角標數字為0 - {clean_site_name}({count}人)"
                    anomalies.append(anomaly)
                    self._log_message(f"🚨 重要異常檢測: {anomaly}", 'WARNING')

                    # 立即截圖記錄此異常
                    try:
                        screenshot_name = f"site_zero_count_anomaly_{tab_name}_{clean_site_name}"
                        self._take_screenshot(screenshot_name, f"{tab_name}_{clean_site_name}_人員數字為0異常", is_critical=True)
                        self._log_message(f"已截圖記錄異常: {screenshot_name}", 'INFO')
                    except Exception as screenshot_e:
                        self._log_message(f"截圖失敗: {screenshot_e}", 'WARNING')

        except Exception as e:
            self._log_message(f"站點數據異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_evacuation_button_anomalies(self, site_name):
        """檢測疏散按鈕異常"""
        anomalies = []

        try:
            # 檢查按下『開始疏散』後是否在指定時間內自動跳回
            revert_timeout = self.config['evacuation_button_revert_timeout_sec']

            # 記錄點擊前的狀態
            initial_time = time.time()

            # 等待並檢查是否自動跳回
            time.sleep(revert_timeout + 0.5)  # 多等0.5秒確認

            try:
                # 檢查是否又變回『開始疏散』
                start_button = self.driver.find_element(By.XPATH, f"//button[contains(text(), '{START_TEXT}')]")
                if start_button:
                    elapsed_time = time.time() - initial_time
                    if elapsed_time <= revert_timeout + 1:  # 允許1秒誤差
                        anomaly = f"{site_name}: 按下『開始疏散』後轉為『結束疏散』，{revert_timeout}秒內又自動跳回『開始疏散』"
                        anomalies.append(anomaly)
                        self._log_message(f"異常檢測: {anomaly}", 'WARNING')
            except NoSuchElementException:
                # 沒有找到開始疏散按鈕，表示狀態正常
                pass

        except Exception as e:
            self._log_message(f"疏散按鈕異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_employee_status_anomalies(self, site_name, employee_element):
        """檢測員工狀態異常"""
        anomalies = []

        try:
            revert_timeout = self.config['employee_status_revert_timeout_sec']

            # 記錄點擊前的狀態
            initial_time = time.time()

            # 等待並檢查是否自動跳回
            time.sleep(revert_timeout + 0.5)  # 多等0.5秒確認

            try:
                # 檢查是否又變回『未疏散』
                unevacuated_button = employee_element.find_element(By.XPATH, ".//button[.//span[text()='未疏散']]")
                if unevacuated_button:
                    elapsed_time = time.time() - initial_time
                    if elapsed_time <= revert_timeout + 1:  # 允許1秒誤差
                        anomaly = f"{site_name}: 按下『未疏散』後轉為『已疏散』，同一人名{revert_timeout}秒內又自動跳回『未疏散』"
                        anomalies.append(anomaly)
                        self._log_message(f"異常檢測: {anomaly}", 'WARNING')
            except NoSuchElementException:
                # 沒有找到未疏散按鈕，表示狀態正常
                pass

        except Exception as e:
            self._log_message(f"員工狀態異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_function_list_anomalies(self):
        """檢測功能列異常"""
        anomalies = []

        try:
            # 檢查功能列是否缺少或重複
            expected_functions = ["戰情報表", "刷卡數據更新", "ERC查看該廠疏散"]

            page_source = self.driver.page_source
            found_functions = []

            for func in expected_functions:
                count = page_source.count(func)
                if count == 0:
                    anomaly = f"功能列缺少: {func}"
                    anomalies.append(anomaly)
                    self._log_message(f"異常檢測: {anomaly}", 'WARNING')
                elif count > 1:
                    anomaly = f"功能列重複: {func} (出現{count}次)"
                    anomalies.append(anomaly)
                    self._log_message(f"異常檢測: {anomaly}", 'WARNING')
                else:
                    found_functions.append(func)

        except Exception as e:
            self._log_message(f"功能列異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_evacuation_summary_anomalies(self, site_name, sure_count, wait_count):
        """檢測疏散總表異常"""
        anomalies = []

        try:
            # 添加詳細的調試信息
            self.q.put({'type': 'log', 'message': f"      檢測疏散總表異常 - 地點: {site_name}", 'level': 'DEBUG'})
            self.q.put({'type': 'log', 'message': f"      原始數據 - 應確認: '{sure_count}' (類型: {type(sure_count)}), 待確認: '{wait_count}' (類型: {type(wait_count)})", 'level': 'DEBUG'})

            # 檢查(應確認/待確認)人數不一致
            if sure_count != 'N/A' and wait_count != 'N/A':
                try:
                    sure_num = int(sure_count)
                    wait_num = int(wait_count)

                    self.q.put({'type': 'log', 'message': f"      轉換後數字 - 應確認: {sure_num}, 待確認: {wait_num}", 'level': 'DEBUG'})

                    # 這裡可以根據業務邏輯定義什麼是"不一致"
                    # 暫時檢查是否有異常的數值組合
                    if sure_num < 0 or wait_num < 0:
                        anomaly = f"{site_name}: (應確認/待確認)人數異常 - 應確認:{sure_count}人, 待確認:{wait_count}人"
                        anomalies.append(anomaly)
                        self.q.put({'type': 'log', 'message': f"異常檢測: {anomaly}", 'level': 'WARNING'})

                except ValueError as ve:
                    anomaly = f"{site_name}: (應確認/待確認)人數格式異常 - 應確認:{sure_count}, 待確認:{wait_count}"
                    anomalies.append(anomaly)
                    self.q.put({'type': 'log', 'message': f"異常檢測: {anomaly} (ValueError: {ve})", 'level': 'WARNING'})

            # 檢查任一單位角標數字為0 或 都為0 (重要異常，需要截圖)
            self.q.put({'type': 'log', 'message': f"      檢查是否為0 - sure_count == '0': {sure_count == '0'}, wait_count == '0': {wait_count == '0'}", 'level': 'DEBUG'})

            if wait_count == '0' and sure_count == '0':
                # 兩個都為0是最嚴重的異常
                anomaly = f"{site_name}: ERC頁面應確認人和待確認人都為0人"
                anomalies.append(anomaly)
                self.q.put({'type': 'log', 'message': f"🚨 重要異常檢測: {anomaly}", 'level': 'WARNING'})

                # 立即截圖記錄此異常
                try:
                    screenshot_name = f"erc_both_zero_anomaly_{site_name.replace(' ', '_').replace('(', '').replace(')', '')}"
                    self._take_screenshot(screenshot_name, f"{site_name}_ERC頁面應確認待確認人數都為0異常", is_critical=True)
                    self.q.put({'type': 'log', 'message': f"已截圖記錄異常: {screenshot_name}", 'level': 'INFO'})
                except Exception as screenshot_e:
                    self.q.put({'type': 'log', 'message': f"截圖失敗: {screenshot_e}", 'level': 'WARNING'})

            elif wait_count == '0':
                # 只有待確認人數為0才是真正的異常（表示沒有人需要疏散）
                anomaly = f"{site_name}: 待確認人數為0 - 應確認:{sure_count}人, 待確認:{wait_count}人"
                anomalies.append(anomaly)
                self.q.put({'type': 'log', 'message': f"異常檢測: {anomaly}", 'level': 'WARNING'})
            elif sure_count == '0' and wait_count != '0':
                # 應確認人數為0但待確認人數不為0是正常情況，不需要報告異常
                self.q.put({'type': 'log', 'message': f"      ✅ 正常情況: {site_name} - 應確認:{sure_count}人, 待確認:{wait_count}人", 'level': 'INFO'})

        except Exception as e:
            self._log_message(f"疏散總表異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _check_erc_page_load_anomalies(self, site_name):
        """檢測ERC頁面載入異常"""
        anomalies = []

        try:
            # 檢查頁面是否正常載入
            page_source = self.driver.page_source
            current_url = self.driver.current_url

            # 檢查是否載入失敗或空白頁面
            if not page_source or len(page_source.strip()) < 100:
                anomaly = f"{site_name}: ERC頁面載入失敗或空白頁面"
                anomalies.append(anomaly)
                self._log_message(f"🚨 重要異常檢測: {anomaly}", 'WARNING')

                # 立即截圖記錄此異常
                try:
                    screenshot_name = f"erc_load_fail_anomaly_{site_name.replace(' ', '_').replace('(', '').replace(')', '')}"
                    self._take_screenshot(screenshot_name, f"{site_name}_ERC頁面載入失敗異常", is_critical=True)
                    self._log_message(f"已截圖記錄異常: {screenshot_name}", 'INFO')
                except Exception as screenshot_e:
                    self._log_message(f"截圖失敗: {screenshot_e}", 'WARNING')

            # 檢查是否包含錯誤訊息
            error_keywords = ['錯誤', 'error', 'Error', '404', '500', '載入失敗', '無法連接', 'timeout']
            for keyword in error_keywords:
                if keyword in page_source:
                    anomaly = f"{site_name}: ERC頁面包含錯誤訊息 - {keyword}"
                    anomalies.append(anomaly)
                    self._log_message(f"🚨 重要異常檢測: {anomaly}", 'WARNING')

                    # 立即截圖記錄此異常
                    try:
                        screenshot_name = f"erc_error_anomaly_{site_name.replace(' ', '_').replace('(', '').replace(')', '')}_{keyword}"
                        self._take_screenshot(screenshot_name, f"{site_name}_ERC頁面錯誤異常_{keyword}", is_critical=True)
                        self._log_message(f"已截圖記錄異常: {screenshot_name}", 'INFO')
                    except Exception as screenshot_e:
                        self._log_message(f"截圖失敗: {screenshot_e}", 'WARNING')
                    break  # 只記錄第一個錯誤

        except Exception as e:
            self._log_message(f"ERC頁面載入異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _revert_all_evacuated_buttons(self, site_name):
        """復歸所有已疏散按鈕變回未疏散狀態"""
        log = self.q.put

        try:
            # 查找所有已疏散按鈕
            evacuated_buttons = self.driver.find_elements(By.XPATH, "//button[contains(., '已疏散')]")

            if not evacuated_buttons:
                log({'type': 'log', 'message': f"          未找到已疏散按鈕，無需復歸", 'level': 'INFO'})
                return True

            log({'type': 'log', 'message': f"          找到 {len(evacuated_buttons)} 個已疏散按鈕，開始批量復歸...", 'level': 'INFO'})

            # 逐一點擊所有已疏散按鈕進行復歸
            reverted_count = 0
            for i, button in enumerate(evacuated_buttons):
                try:
                    # 滾動到按鈕位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                    time.sleep(0.2)

                    # 點擊已疏散按鈕
                    button.click()
                    reverted_count += 1
                    log({'type': 'log', 'message': f"          已復歸第 {i+1} 個已疏散按鈕", 'level': 'INFO'})

                    # 短暫等待狀態變化
                    time.sleep(0.5)
                    self._check_stop()

                except Exception as e:
                    log({'type': 'log', 'message': f"          復歸第 {i+1} 個已疏散按鈕失敗: {e}", 'level': 'WARNING'})
                    continue

            # 驗證復歸結果
            time.sleep(1)  # 等待所有狀態變化完成
            remaining_evacuated = self.driver.find_elements(By.XPATH, "//button[contains(., '已疏散')]")

            if len(remaining_evacuated) == 0:
                log({'type': 'log', 'message': f"          ✅ 成功復歸所有 {reverted_count} 個已疏散按鈕", 'level': 'INFO'})
                self._take_screenshot("all_evacuated_reverted", f"{site_name}_所有已疏散按鈕復歸完成")
                return True
            else:
                log({'type': 'log', 'message': f"          ⚠️ 仍有 {len(remaining_evacuated)} 個已疏散按鈕未復歸", 'level': 'WARNING'})
                return False

        except Exception as e:
            log({'type': 'log', 'message': f"          復歸所有已疏散按鈕時發生錯誤: {e}", 'level': 'ERROR'})
            return False

    def _check_main_page_tabs_anomalies(self):
        """檢測主頁面場域標籤異常"""
        anomalies = []

        try:
            # 檢查場域是否缺少或重複
            expected_tabs = list(SITE_DATA.keys())  # ['廠區', '辦公室', '宿舍', '關係企業']

            page_source = self.driver.page_source
            found_tabs = []

            for tab in expected_tabs:
                count = page_source.count(tab)
                if count == 0:
                    anomaly = f"場域缺少: {tab}"
                    anomalies.append(anomaly)
                    self._log_message(f"異常檢測: {anomaly}", 'WARNING')
                elif count > 1:
                    anomaly = f"場域重複: {tab} (出現{count}次)"
                    anomalies.append(anomaly)
                    self._log_message(f"異常檢測: {anomaly}", 'WARNING')
                else:
                    found_tabs.append(tab)

            # 報告檢測到的異常
            self._report_anomalies(anomalies, "主頁面場域")

        except Exception as e:
            self._log_message(f"主頁面場域異常檢測時發生錯誤: {e}", 'ERROR')

    def _check_add_button_anomalies(self, site_name):
        """檢測新增按鈕異常"""
        anomalies = []

        try:
            self._log_message(f"開始檢測 {site_name} 的新增按鈕...", 'INFO')

            # 根據實際網頁結構尋找新增按鈕
            # 主要檢測: <button><span>新增</span></button> 和帶有 van-icon-add 圖標的按鈕
            add_button_selectors = [
                "//button[.//span[text()='新增']]",  # 精確匹配
                "//button[.//span[contains(text(), '新增')]]",  # 包含匹配
                "//button[.//i[contains(@class, 'van-icon-add')]]",  # 圖標匹配
                "//button[contains(@class, 'van-button') and .//span[contains(text(), '新增')]]"  # 完整結構匹配
            ]

            add_buttons = []
            for selector in add_button_selectors:
                buttons = self.driver.find_elements(By.XPATH, selector)
                if buttons:
                    add_buttons.extend(buttons)
                    self._log_message(f"使用選擇器 '{selector}' 找到 {len(buttons)} 個新增按鈕", 'INFO')

            # 去重
            add_buttons = list(set(add_buttons))
            self._log_message(f"總共找到 {len(add_buttons)} 個新增按鈕", 'INFO')

            if not add_buttons:
                anomaly = f"{site_name}: 無『新增』按鈕"
                anomalies.append(anomaly)
                self._log_message(f"異常檢測: {anomaly}", 'WARNING')
            else:
                # 如果有新增按鈕，測試點擊是否能開啟新增視窗
                try:
                    add_button = add_buttons[0]  # 取第一個新增按鈕
                    button_text = add_button.text or "未知按鈕"
                    self._log_message(f"測試新增按鈕功能: {button_text}", 'INFO')

                    # 檢查點擊前是否已有彈出視窗
                    initial_popups = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-popup') and not(contains(@style, 'display: none'))]")
                    initial_popup_count = len(initial_popups)
                    self._log_message(f"點擊前彈出視窗數量: {initial_popup_count}", 'INFO')

                    # 點擊新增按鈕
                    self.driver.execute_script("arguments[0].scrollIntoView();", add_button)
                    time.sleep(0.5)
                    add_button.click()
                    self._log_message("已點擊新增按鈕，等待視窗開啟...", 'INFO')
                    time.sleep(self.config.get('page_transition_pause', 3))  # 等待視窗開啟

                    # 檢查點擊後是否有新的彈出視窗
                    current_popups = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-popup') and not(contains(@style, 'display: none'))]")
                    current_popup_count = len(current_popups)
                    self._log_message(f"點擊後彈出視窗數量: {current_popup_count}", 'INFO')

                    # 判斷是否成功開啟新增視窗
                    if current_popup_count > initial_popup_count:
                        self._log_message(f"新增按鈕功能正常 - 成功開啟 {current_popup_count - initial_popup_count} 個新視窗", 'INFO')
                    else:
                        # 改善：簡化新增按鈕檢測，不檢測其他類型彈出視窗
                        anomaly = f"{site_name}: 按下『新增』未開啟『新增視窗』"
                        anomalies.append(anomaly)
                        self._log_message(f"異常檢測: {anomaly}", 'WARNING')

                    # 如果有視窗開啟，嘗試關閉它（避免影響後續操作）
                    if current_popup_count > initial_popup_count:
                        try:
                            self._log_message("嘗試關閉新增視窗...", 'INFO')

                            # 方法1: 尋找並點擊關閉按鈕 (X按鈕)
                            close_button_selectors = [
                                "//i[contains(@class, 'van-icon-close')]",  # 根據實際HTML結構
                                "//button[contains(@class, 'close')]",
                                "//div[contains(@class, 'close')]",
                                "//*[contains(text(), '×') or contains(text(), '✕')]"
                            ]

                            close_success = False
                            for selector in close_button_selectors:
                                try:
                                    close_buttons = self.driver.find_elements(By.XPATH, selector)
                                    if close_buttons:
                                        close_buttons[0].click()
                                        time.sleep(2)
                                        self._log_message(f"已使用關閉按鈕關閉新增視窗 (選擇器: {selector})", 'INFO')
                                        close_success = True
                                        break
                                except:
                                    continue

                            # 方法2: 如果找不到關閉按鈕，點擊視窗外部 (overlay)
                            if not close_success:
                                try:
                                    overlay_elements = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-overlay')]")
                                    if overlay_elements:
                                        overlay_elements[0].click()
                                        time.sleep(2)
                                        self._log_message("已點擊視窗外部關閉新增視窗", 'INFO')
                                        close_success = True
                                except:
                                    pass

                            # 方法3: 最後嘗試按ESC鍵
                            if not close_success:
                                try:
                                    from selenium.webdriver.common.keys import Keys
                                    self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                                    time.sleep(2)
                                    self._log_message("已使用ESC鍵嘗試關閉新增視窗", 'INFO')
                                except:
                                    pass

                            # 驗證視窗是否已關閉
                            final_popups = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-popup') and not(contains(@style, 'display: none'))]")
                            if len(final_popups) <= initial_popup_count:
                                self._log_message("新增視窗已成功關閉", 'INFO')
                            else:
                                self._log_message("警告: 新增視窗可能未完全關閉", 'WARNING')

                        except Exception as close_e:
                            self._log_message(f"關閉新增視窗時發生錯誤: {close_e}", 'WARNING')

                except Exception as button_test_e:
                    self._log_message(f"測試新增按鈕功能時發生錯誤: {button_test_e}", 'WARNING')
                    # 如果測試失敗，也視為異常
                    anomaly = f"{site_name}: 新增按鈕測試失敗 - {str(button_test_e)}"
                    anomalies.append(anomaly)

        except Exception as e:
            self._log_message(f"新增按鈕異常檢測時發生錯誤: {e}", 'ERROR')

        return anomalies

    def _report_anomalies(self, anomalies, site_name=""):
        """報告檢測到的異常"""
        if anomalies:
            for anomaly in anomalies:
                # 添加到LINK通知列表
                self.link_notification.add_anomaly_site(site_name or "系統", anomaly)
                # 添加到AUTOMAIL通知列表
                self.automail_notification.add_anomaly_site(site_name or "系統", anomaly)
                self._log_message(f"🚨 檢測到異常: {anomaly}", 'WARNING')

    # ====== 截圖功能相關方法 ======
    def _ensure_screenshot_folder(self):
        """確保截圖資料夾存在"""
        self.pic_step_folder = "PIC_STEP"
        if not os.path.exists(self.pic_step_folder):
            os.makedirs(self.pic_step_folder)
            self.q.put({'type': 'log', 'message': f"已創建截圖資料夾: {self.pic_step_folder}", 'level': 'INFO'})

    def _create_session_folder(self):
        """為當前執行階段創建專用資料夾"""
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        self.current_session_folder = os.path.join(self.pic_step_folder, f"session_{timestamp}")
        if not os.path.exists(self.current_session_folder):
            os.makedirs(self.current_session_folder)
            self.q.put({'type': 'log', 'message': f"已創建執行階段資料夾: {self.current_session_folder}", 'level': 'INFO'})

        # 同時設置日誌檔案
        self._setup_log_file()

    def _setup_log_file(self):
        """設置日誌檔案"""
        try:
            if self.current_session_folder:
                # 在 session 資料夾中創建日誌檔案
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                self.log_file_path = os.path.join(self.current_session_folder, f"execution_log_{timestamp}.txt")
            else:
                # 如果還沒有 session 資料夾，在截圖根目錄創建日誌檔案
                timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
                self.log_file_path = os.path.join(self.pic_step_folder, f"execution_log_{timestamp}.txt")

            # 創建日誌檔案並寫入開始標記
            with open(self.log_file_path, 'w', encoding='utf-8') as f:
                f.write(f"=== 自動化執行日誌 ===\n")
                f.write(f"開始時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"日誌檔案: {self.log_file_path}\n")
                f.write("=" * 50 + "\n\n")

        except Exception as e:
            print(f"設置日誌檔案失敗: {e}")
            self.log_file_path = None

    def _write_to_log_file(self, message, level='INFO'):
        """將訊息寫入日誌檔案"""
        if not self.log_file_path:
            return

        try:
            timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            log_entry = f"[{timestamp}] [{level}] {message}\n"

            with open(self.log_file_path, 'a', encoding='utf-8') as f:
                f.write(log_entry)

        except Exception as e:
            print(f"寫入日誌檔案失敗: {e}")

    def _take_screenshot(self, step_name, description="", is_critical=False):
        """
        截圖並保存到指定資料夾
        is_critical: 是否為關鍵截圖（用於異常檢測）
        """
        # 檢查是否啟用截圖功能
        if not self.config['enable_screenshots'].lower() == 'true':
            return None

        if not self.driver or not self.current_session_folder:
            return None

        try:
            self.screenshot_counter += 1
            timestamp = datetime.datetime.now().strftime('%H%M%S')
            filename = f"{self.screenshot_counter:03d}_{timestamp}_{step_name}.png"
            if description:
                filename = f"{self.screenshot_counter:03d}_{timestamp}_{step_name}_{description}.png"

            # 清理檔名中的特殊字元
            filename = "".join(c for c in filename if c.isalnum() or c in "._-")

            filepath = os.path.join(self.current_session_folder, filename)

            # 核心截圖指令
            self.driver.save_screenshot(filepath)

            # 如果是關鍵截圖，添加到關鍵截圖列表
            if is_critical:
                self.critical_screenshots.append(filepath)
                self.q.put({'type': 'log', 'message': f"📸 已截圖 (關鍵): {filename}", 'level': 'INFO'})
            else:
                # 註解掉非關鍵截圖的日誌，減少輸出
                # self.q.put({'type': 'log', 'message': f"📸 已截圖: {filename}", 'level': 'INFO'})
                pass

            return filepath
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"截圖失敗: {e}", 'level': 'WARNING'})
            return None

    def _safe_input_text(self, element_id, text, description="輸入"):
        """優化版安全文字輸入方法，減少不必要的等待時間"""
        max_retries = 2  # 減少重試次數
        # wait = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10))
        wait = WebDriverWait(self.driver, 1) # 直接將超時時間硬編碼為 1 秒

        for attempt in range(max_retries):
            try:
                if attempt == 0:
                    self.q.put({'type': 'log', 'message': f"正在{description}...", 'level': 'INFO'})
                else:
                    self.q.put({'type': 'log', 'message': f"重試{description} (第{attempt + 1}次)...", 'level': 'INFO'})

                # 一次性等待元素可互動 (合併多個等待條件)
                element = wait.until(EC.element_to_be_clickable((By.ID, element_id)))

                # 減少穩定等待時間
                time.sleep(0.5)  # 從2秒減少到0.5秒

                # 直接使用找到的元素，避免重新查找
                # 滾動到元素位置 (使用instant滾動)
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center', behavior: 'instant'});", element)

                # 獲得焦點並清空輸入
                element.click()
                time.sleep(0.2)  # 減少點擊後等待時間

                # 使用更快的清空方法
                element.clear()
                # 備用清空方法
                self.driver.execute_script("arguments[0].value = '';", element)

                # 輸入文字
                element.send_keys(text)

                # 快速驗證輸入
                time.sleep(0.1)  # 給輸入一點時間生效
                if element.get_attribute('value') == text:
                    self.q.put({'type': 'log', 'message': f"{description}成功", 'level': 'INFO'})
                    return True
                else:
                    # 嘗試使用JavaScript輸入作為備用方案
                    self.driver.execute_script(f"arguments[0].value = '{text}';", element)
                    # 觸發input事件確保網頁識別輸入
                    self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", element)
                    time.sleep(0.1)

                    if element.get_attribute('value') == text:
                        self.q.put({'type': 'log', 'message': f"{description}成功 (使用JavaScript)", 'level': 'INFO'})
                        return True
                    else:
                        self.q.put({'type': 'log', 'message': f"{description}驗證失敗，重試...", 'level': 'WARNING'})

            except Exception as e:
                self.q.put({'type': 'log', 'message': f"{description}失敗 (第{attempt + 1}次): {e}", 'level': 'WARNING'})
                if attempt < max_retries - 1:
                    time.sleep(1)  # 減少重試等待時間

        self.q.put({'type': 'log', 'message': f"{description}最終失敗，已重試{max_retries}次", 'level': 'ERROR'})
        return False

    def _fast_sequential_input(self):
        """快速連續輸入帳號和身分證，間隔0.5秒"""
        try:
            self.q.put({'type': 'log', 'message': "開始快速連續輸入帳號和身分證...", 'level': 'INFO'})

            #wait = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10))
            wait = WebDriverWait(self.driver, 1) # 直接將超時時間硬編碼為 1 秒

            # 步驟1: 輸入帳號 (優化版)
            self.q.put({'type': 'log', 'message': "正在輸入帳號...", 'level': 'INFO'})
            account_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtAccount_E')))

            # 超快速清空並輸入帳號
            self.driver.execute_script(f"arguments[0].value = '{self.config['name']}';", account_element)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", account_element)

            self.q.put({'type': 'log', 'message': "帳號輸入完成", 'level': 'INFO'})

            # 改善：根據超快速模式調整間隔時間
            ultra_fast = self.config.get('ultra_fast_login', False)
            interval_time = 0.1 if ultra_fast else 0.2
            time.sleep(interval_time)

            # 步驟2: 輸入身分證後四位 (優化版)
            self.q.put({'type': 'log', 'message': "正在輸入身分證後四位...", 'level': 'INFO'})
            idno_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtIDNO')))

            # 超快速清空並輸入身分證
            self.driver.execute_script(f"arguments[0].value = '{self.config['password']}';", idno_element)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", idno_element)

            self.q.put({'type': 'log', 'message': "身分證後四位輸入完成", 'level': 'INFO'})

            # 最終驗證
            final_account = account_element.get_attribute('value')
            final_idno = idno_element.get_attribute('value')

            if final_account == self.config['name'] and final_idno == self.config['password']:
                self.q.put({'type': 'log', 'message': "✅ 快速連續輸入成功完成 (間隔0.5秒)", 'level': 'INFO'})

                # 快速模式：條件性異常檢測
                if self.config['disable_login_anomaly_checks'].lower() != 'true':
                    cap1_anomalies = self._check_login_anomalies("CAP第一層")
                    self._report_anomalies(cap1_anomalies, "CAP第一層")

                # 快速模式：條件性截圖 (合併截圖)
                if self.config['skip_login_screenshots'].lower() != 'true':
                    self._take_screenshot("account_and_idno_entered", "帳號和身分證輸入完成")

                # 改善：身分證輸入完成後立即點擊下一步
                self.q.put({'type': 'log', 'message': "身分證輸入完成，立即點擊下一步...", 'level': 'INFO'})
                try:
                    #wait = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10))
                    wait = WebDriverWait(self.driver, 1) # 直接將超時時間硬編碼為 1 秒
                    next_button = wait.until(EC.element_to_be_clickable((By.ID, 'btnNext_E')))
                    next_button.click()
                    self.q.put({'type': 'log', 'message': "✅ 下一步按鈕已點擊", 'level': 'INFO'})

                    # 快速模式：條件性截圖
                    if self.config['skip_login_screenshots'].lower() != 'true':
                        self._take_screenshot("next_clicked_fast", "快速點擊下一步後")

                    return True
                except Exception as e:
                    self.q.put({'type': 'log', 'message': f"❌ 點擊下一步失敗: {e}", 'level': 'ERROR'})
                    return False

            else:
                self.q.put({'type': 'log', 'message': f"❌ 輸入驗證失敗 - 帳號: {final_account}, 身分證: {final_idno}", 'level': 'ERROR'})
                return False

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"❌ 快速連續輸入失敗: {e}", 'level': 'ERROR'})
            return False

    def _fast_password_input_and_login(self):
        """快速密碼輸入：偵測密碼頁面載入完成後1秒輸入密碼，間隔0.5秒後點擊登入"""
        try:
            self.q.put({'type': 'log', 'message': "等待密碼輸入頁面載入...", 'level': 'INFO'})
            wait = WebDriverWait(self.driver, self.config['element_timeout_medium'])

            # 等待密碼頁面載入
            password_element = wait.until(EC.presence_of_element_located((By.ID, 'txtPassword_E')))
            self.q.put({'type': 'log', 'message': "密碼頁面載入完成", 'level': 'INFO'})

            # 改善：根據超快速登入模式調整等待時間
            ultra_fast = self.config.get('ultra_fast_login', False)
            wait_time = 0.2 if ultra_fast else 0.5
            time.sleep(wait_time)

            # 快速模式：條件性異常檢測
            if self.config['disable_login_anomaly_checks'].lower() != 'true':
                cap2_anomalies = self._check_login_anomalies("CAP第二層")
                self._report_anomalies(cap2_anomalies, "CAP第二層")

                password_page_anomalies = self._check_page_loading_anomalies("密碼輸入頁面")
                self._report_anomalies(password_page_anomalies, "密碼輸入頁面")

            # 快速模式：條件性截圖
            if self.config['skip_login_screenshots'].lower() != 'true':
                self._take_screenshot("password_page_loaded", "密碼頁面載入完成")

            # 確保密碼元素可互動
            password_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtPassword_E')))

            # 超快速輸入密碼 (優化版)
            self.q.put({'type': 'log', 'message': "正在快速輸入密碼...", 'level': 'INFO'})
            self.driver.execute_script(f"arguments[0].value = '{self.config['password2']}';", password_element)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", password_element)

            self.q.put({'type': 'log', 'message': "密碼輸入完成", 'level': 'INFO'})

            # 快速模式：條件性截圖
            if self.config['skip_login_screenshots'].lower() != 'true':
                self._take_screenshot("password_entered", "輸入密碼後")

            # 改善：縮短間隔時間到0.2秒後點擊登入
            time.sleep(0.2)

            self.q.put({'type': 'log', 'message': "點擊登入...", 'level': 'INFO'})
            login_button = wait.until(EC.element_to_be_clickable((By.ID, 'btnLogin_E')))
            login_button.click()
            self.q.put({'type': 'log', 'message': "登入按鈕已點擊。", 'level': 'INFO'})

            # 快速模式：條件性截圖
            if self.config['skip_login_screenshots'].lower() != 'true':
                self._take_screenshot("login_clicked", "點擊登入後")

            # 快速模式：減少等待時間
            time.sleep(0.3) # 減少登入後頁面跳轉等待時間

            return True

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"❌ 快速密碼輸入和登入失敗: {e}", 'level': 'ERROR'})
            return False

    def _close_any_popups(self):
        """關閉任何可能的彈出視窗"""
        try:
            # 檢查並關閉彈出視窗
            popups = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-popup') and not(contains(@style, 'display: none'))]")
            if popups:
                self.q.put({'type': 'log', 'message': f"檢測到 {len(popups)} 個彈出視窗，嘗試關閉...", 'level': 'INFO'})

                # 嘗試點擊overlay關閉
                try:
                    overlay = self.driver.find_element(By.XPATH, "//div[contains(@class, 'van-overlay')]")
                    overlay.click()
                    time.sleep(0.5)
                except:
                    pass

                # 嘗試ESC鍵關閉
                try:
                    from selenium.webdriver.common.keys import Keys
                    self.driver.find_element(By.TAG_NAME, 'body').send_keys(Keys.ESCAPE)
                    time.sleep(0.5)
                except:
                    pass

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"關閉彈出視窗時發生錯誤: {e}", 'level': 'WARNING'})

    def _start_timing(self, stage_name):
        """開始記錄某個階段的時間"""
        current_time = time.time()
        self.current_stage_start = current_time
        self.q.put({'type': 'log', 'message': f"⏱️ 開始計時: {stage_name}", 'level': 'INFO'})
        return current_time

    def _end_timing(self, stage_name):
        """結束記錄某個階段的時間並記錄結果"""
        if self.current_stage_start is None:
            self.q.put({'type': 'log', 'message': f"⚠️ 警告: {stage_name} 沒有開始時間記錄", 'level': 'WARNING'})
            return 0

        current_time = time.time()
        duration = current_time - self.current_stage_start
        self.timing_records[stage_name] = duration

        # 格式化時間顯示
        if duration < 1:
            time_str = f"{duration*1000:.0f}ms"
        else:
            time_str = f"{duration:.2f}s"

        self.q.put({'type': 'log', 'message': f"⏱️ {stage_name} 完成，耗時: {time_str}", 'level': 'INFO'})
        self.current_stage_start = None
        return duration

    def _log_timing_summary(self):
        """記錄所有階段的時間總結"""
        if not self.timing_records:
            self.q.put({'type': 'log', 'message': "⚠️ 沒有時間記錄數據", 'level': 'WARNING'})
            return

        self.q.put({'type': 'log', 'message': "\n" + "="*60, 'level': 'INFO'})
        self.q.put({'type': 'log', 'message': "⏱️ 登入流程各階段執行時間統計", 'level': 'INFO'})
        self.q.put({'type': 'log', 'message': "="*60, 'level': 'INFO'})

        total_time = 0
        for stage, duration in self.timing_records.items():
            if duration < 1:
                time_str = f"{duration*1000:.0f}ms"
            else:
                time_str = f"{duration:.2f}s"

            self.q.put({'type': 'log', 'message': f"  {stage:<30}: {time_str}", 'level': 'INFO'})
            total_time += duration

        self.q.put({'type': 'log', 'message': "-"*60, 'level': 'INFO'})
        if total_time < 1:
            total_str = f"{total_time*1000:.0f}ms"
        else:
            total_str = f"{total_time:.2f}s"
        self.q.put({'type': 'log', 'message': f"  {'總計時間':<30}: {total_str}", 'level': 'INFO'})
        self.q.put({'type': 'log', 'message': "="*60, 'level': 'INFO'})

        # 保存時間記錄到檔案
        self._save_timing_to_file()

    def _save_timing_to_file(self):
        """將時間記錄保存到檔案"""
        try:
            if self.current_session_folder:
                timing_file = os.path.join(self.current_session_folder, "timing_records.txt")
                with open(timing_file, 'w', encoding='utf-8') as f:
                    f.write("登入流程各階段執行時間統計\n")
                    f.write("="*60 + "\n")
                    f.write(f"記錄時間: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("-"*60 + "\n")

                    total_time = 0
                    for stage, duration in self.timing_records.items():
                        if duration < 1:
                            time_str = f"{duration*1000:.0f}ms"
                        else:
                            time_str = f"{duration:.2f}s"
                        f.write(f"{stage:<30}: {time_str}\n")
                        total_time += duration

                    f.write("-"*60 + "\n")
                    if total_time < 1:
                        total_str = f"{total_time*1000:.0f}ms"
                    else:
                        total_str = f"{total_time:.2f}s"
                    f.write(f"{'總計時間':<30}: {total_str}\n")
                    f.write("="*60 + "\n")

                self.q.put({'type': 'log', 'message': f"📁 時間記錄已保存到: {timing_file}", 'level': 'INFO'})
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"❌ 保存時間記錄失敗: {e}", 'level': 'ERROR'})

    def _simultaneous_input_login_fields(self):
        """同時輸入帳號和身分證後四位的優化方法"""
        self.q.put({'type': 'log', 'message': "正在同時輸入帳號和身分證...", 'level': 'INFO'})

        try:
            wait = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10))

            # 同時等待兩個輸入框都可用
            account_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtAccount_E')))
            idno_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtIDNO')))

            # 使用JavaScript同時清空和輸入兩個欄位
            script = f"""
                // 清空並輸入帳號
                arguments[0].value = '';
                arguments[0].value = '{self.config['name']}';
                arguments[0].dispatchEvent(new Event('input', {{bubbles: true}}));

                // 清空並輸入身分證
                arguments[1].value = '';
                arguments[1].value = '{self.config['password']}';
                arguments[1].dispatchEvent(new Event('input', {{bubbles: true}}));
            """

            self.driver.execute_script(script, account_element, idno_element)

            # 短暫等待確保輸入生效
            time.sleep(0.3)

            # 驗證兩個欄位的輸入
            account_value = account_element.get_attribute('value')
            idno_value = idno_element.get_attribute('value')

            if account_value == self.config['name'] and idno_value == self.config['password']:
                self.q.put({'type': 'log', 'message': "帳號和身分證同時輸入成功", 'level': 'INFO'})
                return True
            else:
                # 如果同時輸入失敗，回退到逐個輸入
                self.q.put({'type': 'log', 'message': "同時輸入失敗，回退到逐個輸入", 'level': 'WARNING'})
                return False

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"同時輸入失敗: {e}，回退到逐個輸入", 'level': 'WARNING'})
            return False

    def process_erc_page(self, parent_site_name, parent_tab_name):
        """
        處理ERC頁面，包括點擊部門Tab並提取數據。
        """
        self._check_stop()
        self.q.put({'type': 'log', 'message': f"\n--- 進入「{parent_site_name}」的ERC詳細頁面，檢查部門疏散情況 ---", 'level': 'INFO'})

        try:
            # 等待ERC頁面載入完成，例如部門Tab列表出現
            WebDriverWait(self.driver, 20).until(
                EC.presence_of_element_located(self.Locators.ERC_TAB_NAV_LINE)
            )
            self._check_stop()
            time.sleep(self.config.get('reloading_pause', 5)) # 讓頁面穩定

            # 檢測ERC頁面載入異常
            erc_page_anomalies = self._check_page_loading_anomalies(f"疏散總表(主頁)({parent_site_name})")
            self._report_anomalies(erc_page_anomalies, parent_site_name)

            # 檢測ERC頁面載入失敗異常
            erc_load_anomalies = self._check_erc_page_load_anomalies(parent_site_name)
            self._report_anomalies(erc_load_anomalies, parent_site_name)

            # --- 提取總體疏散情況 (位於 dashboard-box 內) ---
            self.q.put({'type': 'log', 'message': f"  提取ERC頁面總體疏散數據...", 'level': 'INFO'})
            html_content_overall = self.driver.page_source
            soup_overall = BeautifulSoup(html_content_overall, 'html.parser')

            # 嘗試多種選擇器來找到正確的元素
            sure_count_selectors = [
                '.dashboard-box .sure-count',
                '.sure-count',
                '[class*="sure-count"]',
                '.dashboard-box .count:first-child',
                '.dashboard-box .count-number:first-child'
            ]

            wait_count_selectors = [
                '.dashboard-box .wait-count',
                '.wait-count',
                '[class*="wait-count"]',
                '.dashboard-box .count:last-child',
                '.dashboard-box .count-number:last-child'
            ]

            overall_sure_count_elem = None
            overall_wait_count_elem = None

            # 嘗試找到應確認人數元素
            for selector in sure_count_selectors:
                overall_sure_count_elem = soup_overall.select_one(selector)
                if overall_sure_count_elem:
                    self.q.put({'type': 'log', 'message': f"    找到應確認人數元素，使用選擇器: {selector}", 'level': 'DEBUG'})
                    break

            # 嘗試找到待確認人數元素
            for selector in wait_count_selectors:
                overall_wait_count_elem = soup_overall.select_one(selector)
                if overall_wait_count_elem:
                    self.q.put({'type': 'log', 'message': f"    找到待確認人數元素，使用選擇器: {selector}", 'level': 'DEBUG'})
                    break

            overall_sure = overall_sure_count_elem.get_text(strip=True) if overall_sure_count_elem else 'N/A'
            overall_wait = overall_wait_count_elem.get_text(strip=True) if overall_wait_count_elem else 'N/A'

            # 添加調試信息
            self.q.put({'type': 'log', 'message': f"    原始提取結果 - 應確認: '{overall_sure}', 待確認: '{overall_wait}'", 'level': 'DEBUG'})

            # 如果找不到元素，嘗試從頁面源碼中直接搜索數字
            if overall_sure == 'N/A' or overall_wait == 'N/A':
                self.q.put({'type': 'log', 'message': f"    CSS選擇器未找到元素，嘗試從頁面源碼中搜索角標數字...", 'level': 'DEBUG'})
                overall_sure, overall_wait = self._extract_badge_numbers_from_source(parent_site_name, html_content_overall)

            overall_status_message = f"應確認: {overall_sure}人, 待確認: {overall_wait}人"
            try:
                if overall_wait != 'N/A' and int(overall_wait) > 0:
                    overall_status_message += " (警告: 待確認人數 > 0)"
                    # 添加異常站點到 LINK 通知列表
                    self.link_notification.add_anomaly_site(parent_site_name, f"待確認人數{overall_wait}人")
                    # 添加異常站點到 AUTOMAIL 通知列表
                    self.automail_notification.add_anomaly_site(parent_site_name, f"待確認人數{overall_wait}人")
            except ValueError:
                overall_status_message += " (人數無法解析)"

            self.q.put({'type': 'log', 'message': f"  總體疏散情況: {overall_status_message}", 'level': 'INFO'})

            # 檢測疏散總表異常
            self.q.put({'type': 'log', 'message': f"    開始檢測疏散總表異常 - 地點: {parent_site_name}, 應確認: {overall_sure}, 待確認: {overall_wait}", 'level': 'DEBUG'})
            summary_anomalies = self._check_evacuation_summary_anomalies(parent_site_name, overall_sure, overall_wait)
            self._report_anomalies(summary_anomalies, parent_site_name)

            # 發送更詳細的結果類型 for CSV
            self.q.put({
                'type': 'erc_overall_result',
                'main_tab': parent_tab_name,
                'main_site_name': parent_site_name.split(' (')[0],
                'main_site_total_people': parent_site_name.split(' (')[1].strip(')'),
                'erc_total_sure': overall_sure,
                'erc_total_wait': overall_wait,
                'detailed_check_result': overall_status_message
            })

            # --- 提取所有部門Tab名稱 ---
            tab_elements = self.driver.find_elements(*self.Locators.ERC_TAB_TEXT)
            tab_names = [elem.text.strip() for elem in tab_elements if elem.text.strip()]
            self.q.put({'type': 'log', 'message': f"  ERC頁面發現部門標籤: {tab_names}", 'level': 'INFO'})

            if not tab_names:
                self.q.put({'type': 'log', 'message': f"  ERC頁面未找到任何部門標籤。", 'level': 'WARNING'})
                return

            # 優化：智能選擇第一個部門進行完整處理
            first_dept = self._select_first_dept_for_full_processing(tab_names, parent_site_name)
            if first_dept:
                self.q.put({'type': 'log', 'message': f"  選擇第一個部門進行完整處理: {first_dept}", 'level': 'INFO'})
                self._process_erc_dept_full(first_dept, parent_site_name, parent_tab_name)
                tab_names.remove(first_dept)
            else:
                self.q.put({'type': 'log', 'message': f"  沒有可用的部門進行完整處理", 'level': 'WARNING'})

            for dept_tab_name in tab_names:
                self._check_stop()
                self._process_erc_dept_fast(dept_tab_name, parent_site_name, parent_tab_name)

                # 在處理完一個部門Tab後等待一段時間，可選
                if not self.status.is_stop_requested():
                    if not interruptible_sleep(self.config['wait_time'], self.status, self.q, reason=f"ERC部門Tab間等待 ({dept_tab_name} -> 下一個)"):
                         raise InterruptedError("ERC部門Tab間等待被停止請求中斷")

        except InterruptedError:
            raise
        except TimeoutException:
            self.q.put({'type': 'log', 'message': f"錯誤: 處理ERC頁面超時，可能未完全載入。", 'level': 'ERROR'})
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"錯誤: 處理ERC頁面時發生未預期錯誤: {e}", 'level': 'ERROR'})

        self.q.put({'type': 'log', 'message': f"--- 完成「{parent_site_name}」的ERC詳細頁面檢查。 ---", 'level': 'INFO'})

    def _test_evacuation_status_for_small_site(self, site_name, people_count, dept_tab_name):
        """
        (修改版) 對人數<=10的地點進行疏散狀態切換測試
        流程: 點擊地點 -> 進入詳情頁 -> 調用新的整合測試函數 -> (由新函數處理返回)
        """
        self._check_stop()
        self.q.put({'type': 'log', 'message': f"      開始對地點「{site_name} ({people_count}人)」進行疏散狀態測試...", 'level': 'INFO'})
        time.sleep(self.config.get('reloading_pause', 5))

        try:
            # 1. 點擊地點按鈕進入詳情頁
            site_button_xpath = f"//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]//span[contains(@class, 'van-button__text') and normalize-space()='{site_name}']{self.Locators.SITE_BUTTON_ANCESTOR}"

            site_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                EC.element_to_be_clickable((By.XPATH, site_button_xpath))
            )

            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", site_button)
            time.sleep(0.5)
            site_button.click()
            time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入

            self.q.put({'type': 'log', 'message': f"        已點擊地點「{site_name}」，等待詳情頁載入...", 'level': 'INFO'})

            # 2. 等待詳情頁載入（以頁面標題變化為標誌）
            WebDriverWait(self.driver, self.config.get('element_timeout_medium', 15)).until(
                lambda driver: "部門詳情" in driver.title or "詳情" in driver.title
            )
            time.sleep(2)  # 等待頁面完全載入

            # 截圖：進入詳情頁
            self._take_screenshot("test_evacuation_detail_page", f"{site_name}進入詳情頁")

            self.q.put({'type': 'log', 'message': f"        詳情頁載入完成，調用整合的員工狀態測試函數...", 'level': 'INFO'})

            # 3. 執行新的整合疏散狀態切換測試函數
            success = self._test_first_unevacuated_employee(site_name)

            if success:
                self.q.put({'type': 'log', 'message': f"        地點「{site_name}」疏散狀態測試成功完成", 'level': 'INFO'})
            else:
                self.q.put({'type': 'log', 'message': f"        地點「{site_name}」疏散狀態測試失敗", 'level': 'WARNING'})

            # 4. 返回 ERC 部門頁面的邏輯已整合到 _test_first_unevacuated_employee 中，此處無需再做
            #    但為確保流程穩定，在返回後重新等待一下並點擊當前 tab
            WebDriverWait(self.driver, self.config.get('element_timeout_medium', 15)).until(
                EC.presence_of_element_located((By.CLASS_NAME, 'van-tabs__nav--line'))
            )
            time.sleep(self.config.get('action_pause', 1))  # 等待頁面穩定

            # 重新點擊部門標籤確保在正確的標籤頁
            self.click_tab_by_name(dept_tab_name, is_erc_tab=True)

            self.q.put({'type': 'log', 'message': f"        已返回 ERC 部門頁面，繼續處理其他地點...", 'level': 'INFO'})

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"        疏散狀態測試失敗: {e}", 'level': 'ERROR'})
            # 嘗試返回 ERC 頁面
            try:
                back_button_xpath = "//div[contains(@class, 'van-nav-bar__left')]"
                back_button = WebDriverWait(self.driver, 5).until(
                    EC.element_to_be_clickable((By.XPATH, back_button_xpath))
                )
                back_button.click()
                time.sleep(2)
            except:
                self.q.put({'type': 'log', 'message': f"        無法返回 ERC 頁面，可能需要手動處理", 'level': 'ERROR'})



    def _test_first_unevacuated_employee(self, site_name, timeout: int = 30) -> bool:
        """
        (完整修正版)
        在部門人員詳情頁面，測試【第一個未疏散】員工的狀態切換功能，並包含返回邏輯。
        """
        try:
            self._check_stop()
            wait = WebDriverWait(self.driver, timeout)
            # 建立一個局部函數，簡化日誌記錄
            log = lambda msg, level='INFO': self.q.put({'type': 'log', 'message': f"          {msg}", 'level': level})

            log("等待部門詳情頁面完全載入...")
            time.sleep(self.config.get('reloading_pause', 5))  # 增加等待時間

            # 截圖：分析頁面結構
            self._take_screenshot("analyze_employee_page", f"{site_name}_員工詳情頁面分析")
            log(f"當前 URL: {self.driver.current_url}")
            log(f"頁面標題: {self.driver.title}")

            # 步驟 1: 尋找未疏散按鈕，如果沒有則嘗試點擊「待確認人」區塊
            log("檢查當前頁面是否需要點擊「待確認人」區塊...")
            unevacuated_buttons = self.driver.find_elements(By.XPATH, "//button[contains(., '未疏散')]")
            
            if not unevacuated_buttons:
                log("當前頁面沒有未疏散按鈕，嘗試點擊「待確認人」區塊進入員工列表...")
                wait_confirm_strategies = [
                    "//div[contains(@class, 'count-title') and contains(., '待確認人：') and .//i[contains(@class, 'van-icon-arrow')]]",
                    "//div[contains(@class, 'count-title') and contains(., '待確認人')]",
                    "//div[contains(., '待確認人') and contains(@class, 'clickable')]"
                ]
                clicked_wait_confirm = False
                for i, strategy in enumerate(wait_confirm_strategies, 1):
                    try:
                        wait_confirm_element = wait.until(EC.element_to_be_clickable((By.XPATH, strategy)))
                        wait_confirm_element.click()
                        time.sleep(2)
                        log(f"策略 {i} 成功點擊待確認人區塊")
                        clicked_wait_confirm = True
                        break
                    except:
                        log(f"策略 {i} 失敗", 'DEBUG')
                        continue

                if not clicked_wait_confirm:
                    log("所有策略都失敗，無法點擊待確認人區塊", 'ERROR')
                    return False
                
                self._take_screenshot("clicked_wait_confirm", f"{site_name}_點擊待確認人區塊後")
                unevacuated_buttons = wait.until(lambda d: d.find_elements(By.XPATH, "//button[contains(., '未疏散')]"))

            if not unevacuated_buttons:
                log("仍然找不到未疏散按鈕，可能此處無人或已全部疏散，測試通過。", 'WARNING')
                # 在這種情況下，我們需要安全地返回
                try:
                    log("找不到測試目標，嘗試返回上一頁...")
                    back_button = wait.until(EC.element_to_be_clickable(self.Locators.GENERIC_BACK_BUTTON))
                    back_button.click()
                    time.sleep(self.config.get('page_transition_pause', 3))
                    log("已成功返回。")
                except Exception as back_e:
                    log(f"返回失敗: {back_e}", 'ERROR')
                return True # 返回 True 因為沒有可測試的目標，不應視為失敗

            # [新增] 完整的點擊、驗證、復歸流程
            first_unevacuated_button = unevacuated_buttons[0]
            log("成功找到第一個未疏散按鈕，開始狀態切換測試...")
            self._check_stop()

            # 步驟 2: 點擊 "未疏散"
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", first_unevacuated_button)
            time.sleep(0.5)
            first_unevacuated_button.click()
            log("已點擊'未疏散'按鈕，等待狀態變為'已疏散'...")
            time.sleep(self.config.get('page_transition_pause', 3))
            self._take_screenshot("test_clicked_unevacuated", f"{site_name}_點擊未疏散後")

            # 步驟 3: 驗證並點擊 "已疏散" 進行復歸
            evacuated_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., '已疏散')]")))
            log("狀態成功變為'已疏散'。現在點擊以復歸...")
            evacuated_button.click()
            time.sleep(self.config.get('page_transition_pause', 3))
            self._take_screenshot("test_reverted_evacuated", f"{site_name}_點擊已疏散復歸後")

            # 步驟 4: 驗證狀態恢復為 "未疏散"
            wait.until(EC.element_to_be_clickable((By.XPATH, "//button[contains(., '未疏散')]")))
            log("狀態成功復歸為'未疏散'。測試完成。")
            self._check_stop()

            # 步驟 5: [關鍵] 點擊返回按鈕回到 ERC 部門頁面
            log("點擊返回按鈕回到 ERC 部門頁面...")
            back_button = wait.until(EC.element_to_be_clickable(self.Locators.GENERIC_BACK_BUTTON))
            back_button.click()
            time.sleep(self.config.get('page_transition_pause', 3))
            log("已成功返回 ERC 部門頁面。")
            self._take_screenshot("returned_to_erc_after_test", f"{site_name}_測試後返回ERC")

            return True

        except (TimeoutException, NoSuchElementException) as e:
            error_msg = f"疏散狀態切換測試時流程中斷 (元素找不到或超時): {type(e).__name__}"
            self.q.put({'type': 'log', 'message': f"          {error_msg}", 'level': 'ERROR'})
            self._take_screenshot("employee_test_error", f"{site_name}_切換測試失敗")
            # 發生錯誤時，仍然嘗試返回上一頁，避免卡死流程
            try:
                log("測試出錯，嘗試強制返回...", 'WARNING')
                back_button = self.driver.find_element(*self.Locators.GENERIC_BACK_BUTTON)
                back_button.click()
                time.sleep(2)
            except:
                pass # 如果連返回都失敗，只能放棄
            return False
        except InterruptedError:
            raise
        except Exception as e:
            error_msg = f"疏散狀態切換測試時發生未預期錯誤: {e}"
            self.q.put({'type': 'log', 'message': f"          {error_msg}", 'level': 'ERROR'})
            self._take_screenshot("employee_test_unexpected_error", f"{site_name}_切換測試異常", is_critical=True)
            try:
                log("測試出錯，嘗試強制返回...", 'WARNING')
                back_button = self.driver.find_element(*self.Locators.GENERIC_BACK_BUTTON)
                back_button.click()
                time.sleep(2)
            except:
                pass
            return False

    def _cleanup_memory(self):
        """清理記憶體和瀏覽器快取"""
        try:
            # 清理瀏覽器快取
            self.driver.execute_script("window.localStorage.clear();")
            self.driver.execute_script("window.sessionStorage.clear();")
            
            # 強制垃圾回收
            import gc
            gc.collect()
            
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"記憶體清理失敗: {e}", 'level': 'WARNING'})

            if len(unevacuated_buttons) == 0:
                log("當前頁面沒有未疏散按鈕，嘗試點擊「待確認人」區塊進入員工列表...")

                # 參考 ERC_TEST_IMPROVED.py 的策略，尋找並點擊待確認人區塊
                wait_confirm_strategies = [
                    "//div[contains(@class, 'count-title') and contains(., '待確認人：')]",
                    "//*[contains(text(), '待確認人：')]",
                    "//div[contains(., '待確認人') and contains(@class, 'van-cell')]",
                    "//*[contains(text(), '待確認')]"
                ]

                clicked_wait_confirm = False
                for i, xpath in enumerate(wait_confirm_strategies, 1):
                    try:
                        log(f"嘗試策略 {i}: 尋找待確認人區塊")
                        wait_element = WebDriverWait(self.driver, 5).until(
                            EC.element_to_be_clickable((By.XPATH, xpath))
                        )
                        self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", wait_element)
                        time.sleep(self.config.get('action_pause', 1))
                        wait_element.click()
                        log(f"策略 {i} 成功點擊待確認人區塊")
                        self._take_screenshot("clicked_wait_confirm", f"{site_name}_點擊待確認人區塊後")
                        time.sleep(self.config.get('reloading_pause', 5))  # 等待頁面載入
                        clicked_wait_confirm = True
                        break
                    except TimeoutException:
                        log(f"策略 {i} 失敗")
                        continue

                if not clicked_wait_confirm:
                    log("所有策略都無法找到待確認人區塊", 'ERROR')
                    return False

                # 重新尋找未疏散按鈕
                unevacuated_buttons = self.driver.find_elements(By.XPATH, "//button[contains(., '未疏散')]")
                log(f"點擊待確認人區塊後，找到 {len(unevacuated_buttons)} 個未疏散按鈕")

            if len(unevacuated_buttons) == 0:
                log("仍然沒有找到未疏散按鈕，可能所有員工都已疏散", 'WARNING')
                return False

            # 使用第一個未疏散按鈕
            first_unevacuated_button = unevacuated_buttons[0]
            log("成功找到第一個未疏散按鈕")

            # 嘗試獲取員工姓名 (用於日誌，非必要)
            employee_name = "第一位員工"
            try:
                # 嘗試從按鈕的父元素中獲取員工姓名
                parent_element = first_unevacuated_button.find_element(By.XPATH, "./ancestor::div[1]")
                name_text = parent_element.text.strip()
                if name_text and len(name_text) < 50:  # 避免獲取過長的文本
                    employee_name = name_text.split('\n')[0]  # 取第一行作為姓名
            except Exception:
                pass # 獲取姓名失敗不影響流程

            log(f"找到目標員工: '{employee_name}'。開始狀態切換測試...")
            self._check_stop()

            # 步驟 2: 點擊 "未疏散" 按鈕
            log(f"步驟 2: 點擊 '{employee_name}' 的 '未疏散' 按鈕。")
            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", first_unevacuated_button)
            time.sleep(self.config.get('action_pause', 1))
            first_unevacuated_button.click()
            log("已點擊第一個未疏散按鈕")
            time.sleep(self.config.get('page_transition_pause', 3)) # 等待狀態變化
            self._check_stop()
            self._take_screenshot("test_clicked_unevacuated", f"{site_name}_{employee_name}_點擊未疏散後")

            # 步驟 3: 等待並驗證狀態變為 "已疏散"
            log("步驟 3: 驗證狀態是否變為 '已疏散'。")
            try:
                # 尋找已疏散按鈕
                evacuated_buttons = self.driver.find_elements(By.XPATH, "//button[contains(., '已疏散')]")
                if len(evacuated_buttons) > 0:
                    log("狀態成功變為已疏散")
                    evacuated_button = evacuated_buttons[0]
                else:
                    log("狀態未能變為已疏散", 'ERROR')
                    return False
            except Exception as e:
                log(f"檢查已疏散狀態失敗: {e}", 'ERROR')
                return False

            self._check_stop()

            # 步驟 4: 點擊 "已疏散" 按鈕，進行復歸
            log(f"步驟 4: 點擊 '{employee_name}' 的 '已疏散' 按鈕進行復歸。")
            evacuated_button.click()
            log("已點擊已疏散按鈕進行復原")
            time.sleep(self.config.get('page_transition_pause', 3)) # 等待狀態變化
            self._check_stop()
            self._take_screenshot("test_clicked_evacuated", f"{site_name}_{employee_name}_點擊已疏散後")

            # 步驟 5: 等待並驗證狀態變回 "未疏散"
            log("步驟 5: 驗證狀態是否變回 '未疏散'。")
            try:
                # 檢查是否復原為未疏散
                final_unevacuated = self.driver.find_elements(By.XPATH, "//button[contains(., '未疏散')]")
                if len(final_unevacuated) > 0:
                    log("狀態成功復原為未疏散")
                else:
                    log("狀態未能復原為未疏散", 'ERROR')
                    return False
            except Exception as e:
                log(f"檢查復原狀態失敗: {e}", 'ERROR')
                return False

            log(f"🎉 員工 '{employee_name}' 疏散狀態切換測試全部完成")

            # 步驟 5.1: 復歸所有已疏散按鈕
            log("步驟 5.1: 檢查並復歸所有已疏散按鈕...")
            self._revert_all_evacuated_buttons(site_name)

            # 步驟 5.5: 檢測新增按鈕異常（快速模式檢查）
            skip_popup_testing = self.config.get('skip_popup_testing_after_first', False)
            if skip_popup_testing and len(self.wait_confirm_tested_main_sites) > 0:
                log("步驟 5.5: 快速模式 - 跳過新增按鈕詳細檢測以加快速度")
            else:
                log("步驟 5.5: 檢測新增按鈕異常...")
                try:
                    add_button_anomalies = self._check_add_button_anomalies(site_name)
                    self._report_anomalies(add_button_anomalies, site_name)
                    if add_button_anomalies:
                        log(f"檢測到 {len(add_button_anomalies)} 個新增按鈕異常")
                    else:
                        log("新增按鈕檢測正常")
                except Exception as e:
                    log(f"新增按鈕異常檢測失敗: {e}", 'ERROR')

            # 步驟 6: 點擊返回按鈕回到 ERC 部門頁面
            log("步驟 6: 點擊返回按鈕回到 ERC 部門頁面...")

            # 確保沒有彈出視窗遮擋返回按鈕
            try:
                remaining_popups = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-popup') and not(contains(@style, 'display: none'))]")
                if remaining_popups:
                    log(f"檢測到 {len(remaining_popups)} 個未關閉的彈出視窗，嘗試再次關閉...", 'WARNING')
                    for popup in remaining_popups:
                        try:
                            # 嘗試點擊視窗外部關閉
                            overlay = self.driver.find_element(By.XPATH, "//div[contains(@class, 'van-overlay')]")
                            overlay.click()
                            time.sleep(self.config.get('action_pause', 1))
                        except:
                            pass
            except:
                pass

            try:
                # 等待返回按鈕可點擊，增加等待時間
                back_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]")))

                # 滾動到返回按鈕位置
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", back_button)
                time.sleep(self.config.get('action_pause', 1))

                # 嘗試點擊返回按鈕
                back_button.click()
                time.sleep(self.config.get('page_transition_pause', 3))
                log("已成功點擊返回按鈕")
                self._take_screenshot("returned_to_erc_after_test", f"{site_name}測試後返回ERC")
                time.sleep(2)
                log("已成功返回 ERC 部門頁面")

            except TimeoutException:
                log("找不到返回按鈕", 'WARNING')
                try:
                    # 嘗試其他返回按鈕
                    back_button = wait.until(EC.element_to_be_clickable(self.Locators.ERC_BACK_BUTTON_FROM_DETAIL))
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", back_button)
                    time.sleep(self.config.get('action_pause', 1))
                    back_button.click()
                    time.sleep(self.config.get('page_transition_pause', 3))
                    log("使用疏散詳情返回按鈕成功")
                except Exception as back_e:
                    log(f"所有返回按鈕都失敗: {back_e}", 'ERROR')

            except Exception as click_e:
                log(f"點擊返回按鈕時發生錯誤: {click_e}", 'ERROR')
                # 改善：使用多種策略處理返回按鈕點擊問題
                return_success = False

                # 策略1: 如果是元素被遮擋，先關閉彈出視窗再重試
                if "element click intercepted" in str(click_e):
                    try:
                        log("檢測到元素被遮擋，嘗試關閉彈出視窗...", 'INFO')
                        self._close_any_popups()
                        time.sleep(1)

                        # 重新嘗試點擊
                        back_button = wait.until(EC.element_to_be_clickable((By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]")))
                        back_button.click()
                        time.sleep(self.config.get('page_transition_pause', 3))
                        log("關閉彈出視窗後成功點擊返回按鈕")
                        return_success = True
                    except Exception as retry_e:
                        log(f"關閉彈出視窗後重試失敗: {retry_e}", 'WARNING')

                # 策略2: 使用JavaScript點擊
                if not return_success:
                    try:
                        log("嘗試使用JavaScript點擊返回按鈕...", 'INFO')
                        back_button = self.driver.find_element(By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]")
                        self.driver.execute_script("arguments[0].click();", back_button)
                        time.sleep(self.config.get('page_transition_pause', 3))
                        log("使用JavaScript成功點擊返回按鈕")
                        return_success = True
                    except Exception as js_e:
                        log(f"JavaScript點擊失敗: {js_e}", 'WARNING')

                # 策略3: 嘗試點擊返回按鈕的子元素
                if not return_success:
                    try:
                        log("嘗試點擊返回按鈕的子元素...", 'INFO')
                        back_icon = self.driver.find_element(By.XPATH, "//div[contains(@class, 'van-nav-bar__left')]//i")
                        self.driver.execute_script("arguments[0].click();", back_icon)
                        time.sleep(self.config.get('page_transition_pause', 3))
                        log("點擊返回圖標成功")
                        return_success = True
                    except Exception as icon_e:
                        log(f"點擊返回圖標失敗: {icon_e}", 'WARNING')

                if return_success:
                    self._take_screenshot("returned_to_erc_after_test", f"{site_name}測試後返回ERC")
                else:
                    log("所有返回按鈕點擊策略都失敗", 'ERROR')
            return True

        except (TimeoutException, NoSuchElementException) as e:
            error_msg = f"❌ 疏散狀態切換測試時流程中斷 (元素找不到或超時): {type(e).__name__}"
            self.q.put({'type': 'log', 'message': f"          {error_msg}", 'level': 'ERROR'})
            self._take_screenshot("employee_test_error", f"{site_name}_切換測試失敗")
            # Try to navigate back anyway to prevent getting stuck
            try:
                back_button = self.driver.find_element(*self.Locators.GENERIC_BACK_BUTTON)
                back_button.click()
                time.sleep(2)
            except:
                pass
            return False
        except InterruptedError:
            raise
        except Exception as e:
            error_msg = f"❌ 疏散狀態切換測試時發生未預期錯誤: {e}"
            self.q.put({'type': 'log', 'message': f"          {error_msg}", 'level': 'ERROR'})
            self._take_screenshot("employee_test_unexpected_error", f"{site_name}_切換測試異常", is_critical=True)
            # Try to navigate back anyway to prevent getting stuck
            try:
                back_button = self.driver.find_element(*self.Locators.GENERIC_BACK_BUTTON)
                back_button.click()
                time.sleep(2)
            except:
                pass
            return False

    def _process_unconfirmed_persons_details_fast(self, dept_tab_name):
        """
        快速模式的 ERC 人員測試功能 - 僅進行基本檢查，跳過詳細測試
        """
        self._check_stop()

        try:
            self.q.put({'type': 'log', 'message': f"        快速模式：檢查「{dept_tab_name}」部門是否有待確認人...", 'level': 'INFO'})

            # 快速檢查是否有待確認人區塊，但不進行詳細測試
            wait_confirm_clickable_xpath = self.Locators.WAIT_CONFIRM_CLICKABLE_XPATH
            wait_confirm_elements = self.driver.find_elements(By.XPATH, wait_confirm_clickable_xpath)

            if wait_confirm_elements:
                self.q.put({'type': 'log', 'message': f"        快速模式：在「{dept_tab_name}」找到待確認人區塊，但跳過詳細測試以加快速度", 'level': 'INFO'})
            else:
                self.q.put({'type': 'log', 'message': f"        快速模式：在「{dept_tab_name}」未找到待確認人區塊", 'level': 'INFO'})

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"        快速模式檢查「{dept_tab_name}」時發生錯誤: {str(e)}", 'level': 'ERROR'})

    def _process_unconfirmed_persons_details(self, dept_tab_name):
        """
        新增的 ERC 人員測試功能
        在ERC部門頁面，檢查並處理"待確認人"的詳細操作。
        根據實際操作記錄的正確流程：
        1. 點選 "待確認人：" 區塊 → 進入部門詳情頁面
        2. 點選 "未疏散" 按鈕 → 狀態變為已疏散
        3. 點選 "已疏散" 按鈕 → 復歸為未疏散
        4. 點選 "疏散詳情" 返回 ERC 頁面

        重要：不需要滑到最下方，所有操作都在頁面頂部進行
        """
        self._check_stop()

        # 檢查是否啟用快速模式
        skip_popup_testing = self.config.get('skip_popup_testing_after_first', False)
        if skip_popup_testing and len(self.wait_confirm_tested_main_sites) > 0:
            self._process_unconfirmed_persons_details_fast(dept_tab_name)
            return

        navigated_to_details = False

        try:
            self.q.put({'type': 'log', 'message': f"        檢查「{dept_tab_name}」部門是否有待確認人...", 'level': 'INFO'})

            # 根據實際操作記錄，查找可點擊的 "待確認人：" 區塊
            wait_confirm_clickable_xpath = self.Locators.WAIT_CONFIRM_CLICKABLE_XPATH
            wait_confirm_elements = self.driver.find_elements(By.XPATH, wait_confirm_clickable_xpath)

            if not wait_confirm_elements:
                self.q.put({'type': 'log', 'message': f"        在「{dept_tab_name}」未找到可點擊的'待確認人'區塊，跳過處理。", 'level': 'INFO'})
                return

            # 檢查每個找到的待確認人區塊
            for wait_confirm_element in wait_confirm_elements:
                try:
                    # 提取待確認人數
                    wait_count_span = wait_confirm_element.find_element(*self.Locators.WAIT_COUNT_SPAN)
                    wait_count = int(wait_count_span.text.strip())
                except (NoSuchElementException, ValueError):
                    self.q.put({'type': 'log', 'message': f"        警告: 在'待確認人'區塊中未找到有效的人數，跳過。", 'level': 'WARNING'})
                    continue

                if wait_count == 0:
                    self.q.put({'type': 'log', 'message': f"        「{dept_tab_name}」待確認人數為0，無需處理。", 'level': 'INFO'})
                    continue

                self.q.put({'type': 'log', 'message': f"        發現「{dept_tab_name}」有 {wait_count} 位待確認人，開始處理...", 'level': 'WARNING'})

                # 截圖：處理待確認人前
                self._take_screenshot("before_handle_unconfirmed", f"{dept_tab_name}處理待確認人前")

                # 1. 點擊 "待確認人：" 區塊，進入部門詳情頁面
                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", wait_confirm_element)
                time.sleep(0.5)
                wait_confirm_element.click()
                time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入
                self.q.put({'type': 'log', 'message': f"          已點擊'待確認人'區塊，等待進入部門詳情頁面...", 'level': 'INFO'})
                navigated_to_details = True

                # 等待部門詳情頁載入（以頁面標題變為 "部門詳情" 為標誌）
                WebDriverWait(self.driver, self.config.get('element_timeout_medium', 15)).until(
                    lambda driver: "部門詳情" in driver.title
                )
                self.q.put({'type': 'log', 'message': f"          部門詳情頁面載入成功，標題: {self.driver.title}", 'level': 'INFO'})
                self._check_stop()
                time.sleep(2)  # 根據實際記錄，給頁面充分載入時間

                # 截圖：進入詳情頁面後
                self._take_screenshot("entered_detail_page", f"{dept_tab_name}進入詳情頁面")

                # 檢測疏散詳情頁面載入異常
                detail_page_anomalies = self._check_page_loading_anomalies(f"疏散詳情({dept_tab_name})")
                self._report_anomalies(detail_page_anomalies, dept_tab_name)

                # 2. 點擊 "未疏散" 按鈕（狀態會變為已疏散）
                self.q.put({'type': 'log', 'message': f"          尋找並點擊'未疏散'按鈕...", 'level': 'INFO'})

                # 根據實際操作，使用更簡單的定位策略
                unevacuated_button_xpath = self.Locators.UNEVACUATED_BUTTON[1]

                try:
                    unevacuated_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                        EC.element_to_be_clickable((By.XPATH, unevacuated_button_xpath))
                    )

                    self.q.put({'type': 'log', 'message': f"          找到'未疏散'按鈕，準備點擊...", 'level': 'INFO'})
                    # 不需要滾動，根據記錄所有操作都在頁面頂部
                    unevacuated_button.click()
                    time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入
                    self.q.put({'type': 'log', 'message': f"          已點擊'未疏散'按鈕，狀態應變為'已疏散'", 'level': 'INFO'})
                    self._check_stop()
                    time.sleep(2)  # 等待狀態變化

                    # 檢測員工狀態異常（是否自動跳回）
                    employee_anomalies = self._check_employee_status_anomalies(dept_tab_name, unevacuated_button)
                    self._report_anomalies(employee_anomalies, dept_tab_name)

                    # 截圖：點擊未疏散按鈕後
                    self._take_screenshot("clicked_unevacuated", f"{dept_tab_name}點擊未疏散按鈕後")

                    # 3. 點擊 "已疏散" 按鈕進行復歸（變回未疏散）
                    self.q.put({'type': 'log', 'message': f"          尋找並點擊'已疏散'按鈕進行復歸...", 'level': 'INFO'})

                    # 根據實際操作記錄，不需要滑到最下方，直接尋找已疏散按鈕
                    evacuated_button_xpath = self.Locators.EVACUATED_BUTTON[1]
                    evacuated_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                        EC.element_to_be_clickable((By.XPATH, evacuated_button_xpath))
                    )

                    self.q.put({'type': 'log', 'message': f"          找到'已疏散'按鈕，點擊進行復歸...", 'level': 'INFO'})
                    evacuated_button.click()
                    time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入
                    self.q.put({'type': 'log', 'message': f"          已點擊'已疏散'按鈕，狀態應已復歸為'未疏散'", 'level': 'INFO'})
                    self._check_stop()
                    time.sleep(2)  # 等待狀態復歸

                    # 截圖：復歸操作後
                    self._take_screenshot("after_revert", f"{dept_tab_name}復歸操作後")

                except TimeoutException:
                    self.q.put({'type': 'log', 'message': f"          警告: 未找到'未疏散'或'已疏散'按鈕，可能頁面結構不同", 'level': 'WARNING'})
                except Exception as btn_e:
                    self.q.put({'type': 'log', 'message': f"          錯誤: 處理疏散按鈕時發生錯誤: {btn_e}", 'level': 'ERROR'})

                # 處理完一個待確認人區塊後跳出循環
                break

        except InterruptedError:
            raise
        except TimeoutException:
            self.q.put({'type': 'log', 'message': f"        錯誤: 處理'待確認人'流程超時，可能元素未找到或頁面未載入。", 'level': 'ERROR'})
        except NoSuchElementException:
            self.q.put({'type': 'log', 'message': f"        錯誤: 處理'待確認人'流程中找不到指定元素。", 'level': 'ERROR'})
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"        錯誤: 處理'待確認人'時發生未預期錯誤: {e}", 'level': 'ERROR'})

        finally:
            # 4. 無論成功或失敗，只要進入了詳情頁，就嘗試返回 ERC 部門頁面
            if navigated_to_details:
                try:
                    self._check_stop()
                    self.q.put({'type': 'log', 'message': f"          操作完成，點擊'疏散詳情'返回 部門疏散情況 部門頁面...", 'level': 'INFO'})

                    # 根據實際操作記錄，等待並點擊疏散詳情返回按鈕
                    back_to_erc_xpath = self.Locators.ERC_BACK_BUTTON_FROM_DETAIL[1]
                    back_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                        EC.element_to_be_clickable((By.XPATH, back_to_erc_xpath))
                    )
                    back_button.click()
                    time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入

                    # 等待返回到 ERC 頁面（標題變為 "廠區疏散詳情"）
                    WebDriverWait(self.driver, self.config.get('element_timeout_medium', 15)).until(
                        lambda driver: "廠區疏散詳情" in driver.title or "ErcDetail" in driver.current_url
                    )
                    self.q.put({'type': 'log', 'message': f"          已成功返回 ERC 部門頁面，標題: {self.driver.title}", 'level': 'INFO'})

                    # 截圖：返回ERC部門頁面後
                    self._take_screenshot("returned_to_erc", f"{dept_tab_name}返回ERC部門頁面")

                    time.sleep(self.config.get('action_pause', 1)) # 等待頁面穩定
                except InterruptedError:
                    raise
                except Exception as back_e:
                    self.q.put({'type': 'log', 'message': f"        嚴重錯誤: 從部門詳情頁返回 ERC 部門頁面失敗: {back_e}。流程可能中斷。", 'level': 'ERROR'})
                    # 如果連返回都失敗，可能需要更強制的措施，比如強制導航，但這裡暫時只記錄錯誤

    def _force_navigate_to_main_page(self):
        """強制導航回主頁面"""
        self.q.put({'type': 'log', 'message': "  強制導航回主頁面...", 'level': 'INFO'})
        try:
            self.driver.get(self.config['url'])
            WebDriverWait(self.driver, self.config.get('element_timeout_long', 10)).until(EC.presence_of_element_located(self.Locators.SITE_BOX_ITEM))
            self.q.put({'type': 'log', 'message': "  強制導航回主頁面成功。", 'level': 'INFO'})
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"  強制導航回主頁面失敗: {e}", 'level': 'ERROR'})
            raise

    def _attempt_to_return_to_main_list(self, context_description=""):
        """嘗試返回主列表頁面"""
        try:
            self.q.put({'type': 'log', 'message': f"  {context_description} - 嘗試返回主列表頁面...", 'level': 'INFO'})
            # 嘗試點擊通用返回按鈕
            generic_back_button_xpath = "//div[contains(@class, 'van-nav-bar__left')]"
            back_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                EC.element_to_be_clickable((By.XPATH, generic_back_button_xpath))
            )
            back_button.click()
            time.sleep(2)
            self.q.put({'type': 'log', 'message': f"  {context_description} - 已返回主列表頁面。", 'level': 'INFO'})
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"  {context_description} - 返回主列表失敗: {e}", 'level': 'ERROR'})

    def _post_cycle_reversion_check(self):
        """
        在主自動化週期結束後，逐個檢查所有地點，確保所有「結束疏散」狀態都被復歸。
        """
        self._check_stop()
        self.q.put({'type': 'log', 'message': "\n--- 開始執行事後復歸檢查 (確保所有地點復歸為 '開始疏散') ---", 'level': 'INFO'})

        # 確保在開始檢查前回到主頁面
        try:
            self._force_navigate_to_main_page()
        except InterruptedError:
            raise
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"事後復歸檢查: 導航回主頁失敗，無法進行復歸檢查: {e}", 'level': 'ERROR'})
            return

        tabs_to_check = ['廠區', '辦公室', '宿舍', '關係企業'] # 與主流程相同的 Tab 列表

        for tab_name in tabs_to_check:
            self._check_stop()
            self.q.put({'type': 'log', 'message': f"\n事後復歸檢查: 切換到「{tab_name}」標籤...", 'level': 'INFO'})

            if not self.click_tab_by_name(tab_name):
                self.q.put({'type': 'log', 'message': f"事後復歸檢查: 切換到「{tab_name}」失敗，跳過此標籤的復歸檢查。", 'level': 'ERROR'})
                continue

            # 重新提取地點信息，確保是最新的元素列表
            sites_info = self.extract_and_display_data(tab_name)

            if not sites_info:
                self.q.put({'type': 'log', 'message': f"事後復歸檢查: 「{tab_name}」標籤下沒有地點可供檢查。", 'level': 'INFO'})
                continue

            for raw_site_name, combined_site_name, _ in sites_info:
                self._check_stop()
                self.q.put({'type': 'log', 'message': f"事後復歸檢查: 檢查地點「{combined_site_name}」的復歸狀態...", 'level': 'INFO'})

                try:
                    # 每次循環都重新尋找 site_button
                    current_site_buttons = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]//button")
                    target_button = None
                    for btn in current_site_buttons:
                        try:
                            if raw_site_name in btn.text:
                                target_button = btn
                                break
                        except StaleElementReferenceException:
                            # 元素可能在檢查過程中過時，重新查找
                            current_site_buttons = self.driver.find_elements(By.XPATH, "//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]//button")
                            continue # 繼續外層循環查找

                    if not target_button:
                        self.q.put({'type': 'log', 'message': f"事後復歸檢查: 未能在 [{tab_name}] 找到地點按鈕「{raw_site_name}」，跳過此地點。", 'level': 'WARNING'})
                        continue

                    site_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                        EC.element_to_be_clickable(target_button)
                    )
                    self.driver.execute_script("arguments[0].scrollIntoView();", site_button)
                    time.sleep(0.3)
                    self._check_stop()

                    site_button.click()
                    self.q.put({'type': 'log', 'message': f"事後復歸檢查: 成功點擊地點「{combined_site_name}」。", 'level': 'INFO'})
                    time.sleep(2) # 等待詳細頁面載入
                    self._check_stop()

                    # 檢查是否存在「結束疏散」按鈕
                    page_source = self.driver.page_source
                    if FORBIDDEN_TEXT in page_source:
                        self.q.put({'type': 'log', 'message': f"事後復歸檢查: 在地點「{combined_site_name}」詳細頁面檢測到「{FORBIDDEN_TEXT}」按鈕，嘗試點擊復歸。", 'level': 'WARNING'})
                        try:
                            end_evac_button_xpath = f"//button[contains(., '{FORBIDDEN_TEXT}')]"
                            end_evac_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                                EC.element_to_be_clickable((By.XPATH, end_evac_button_xpath))
                            )
                            self.driver.execute_script("arguments[0].scrollIntoView();", end_evac_button)
                            time.sleep(0.3)
                            end_evac_button.click()
                            self.q.put({'type': 'log', 'message': f"事後復歸檢查: 成功點擊「{FORBIDDEN_TEXT}」按鈕，已復歸。", 'level': 'INFO'})
                            time.sleep(2) # 等待頁面返回主列表
                            self._check_stop()
                        except TimeoutException:
                            self.q.put({'type': 'log', 'message': f"事後復歸檢查: 點擊「{FORBIDDEN_TEXT}」按鈕超時，可能未成功復歸。", 'level': 'ERROR'})
                        except Exception as e:
                            self.q.put({'type': 'log', 'message': f"事後復歸檢查: 點擊「{FORBIDDEN_TEXT}」按鈕時發生錯誤: {e}", 'level': 'ERROR'})
                    else:
                        self.q.put({'type': 'log', 'message': f"事後復歸檢查: 地點「{combined_site_name}」已處於復歸狀態或無「{FORBIDDEN_TEXT}」按鈕，無需操作。", 'level': 'INFO'})

                    # 無論是否點擊了「結束疏散」，都需要返回主列表頁面
                    self._attempt_to_return_to_main_list(f"檢查地點 {combined_site_name} 後")

                except InterruptedError:
                    raise
                except Exception as e:
                    self.q.put({'type': 'log', 'message': f"事後復歸檢查: 處理地點「{combined_site_name}」時發生未預期錯誤: {e}。嘗試返回主列表。", 'level': 'ERROR'})

        self.q.put({'type': 'log', 'message': "--- 事後復歸檢查完成。 ---", 'level': 'INFO'})
    
    # [保留不變] check_page_content
    def check_page_content(self, page_description):
        self._check_stop()
        warnings = []
        check_result_string = "未檢測到必要文字"
        try:
            WebDriverWait(self.driver, 5).until(EC.presence_of_element_located((By.TAG_NAME, 'body')))
            self._check_stop()
            time.sleep(0.5)
            self._check_stop()
            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')
            page_text = soup.get_text()
            all_required_found = True
            for text in REQUIRED_TEXTS:
                if text not in page_text:
                    warnings.append(f"找不到必要文字: '{text}'")
                    all_required_found = False
            if all_required_found:
                check_result_string = "檢查通過"
                self.q.put({'type': 'log', 'message': f"檢查 [{page_description}] 頁面內容... 檢查通過。", 'level': 'INFO'})
            else:
                check_result_string = f"警告: {'; '.join(warnings)}"
                self.q.put({'type': 'log', 'message': f"檢查 [{page_description}] 頁面內容... {check_result_string}", 'level': 'WARNING'})
            return check_result_string
        except InterruptedError:
             raise
        except Exception as e:
            error_message = f"錯誤: 檢查 [{page_description}] 頁面內容時發生錯誤: {e}"
            self.q.put({'type': 'log', 'message': error_message, 'level': 'ERROR'})
            return f"檢查錯誤: {e}"

    # [保留不變] extract_and_display_data
    def extract_and_display_data(self, tab_name):
        self._check_stop()
        self.q.put({'type': 'log', 'message': f"\n提取並顯示 [{tab_name}] 數據：", 'level': 'INFO'})
        site_data_for_processing = []
        try:
            WebDriverWait(self.driver, 20).until(EC.presence_of_element_located((By.CLASS_NAME, 'site-box-item')))
            self._check_stop()
            time.sleep(self.config.get('action_pause', 1))
            self._check_stop()
            html_content = self.driver.page_source
            soup = BeautifulSoup(html_content, 'html.parser')
            active_panel_soup = soup.find('div', class_=lambda c: c and 'van-tab__panel' in c.split(), style=lambda s: s is None or 'display: none' not in str(s).lower())
            if not active_panel_soup:
                self.q.put({'type': 'log', 'message': f"  錯誤：在 [{tab_name}] 中找不到活動的標籤頁面板。", 'level': 'ERROR'})
                return []
            items = active_panel_soup.select('.site-box-item')
            if not items:
                no_data_text = active_panel_soup.get_text()
                if '查無資料' in no_data_text or '無數據' in no_data_text or '查無資訊' in no_data_text:
                     self.q.put({'type': 'log', 'message': f"  提示: [{tab_name}] 可能查無資料。", 'level': 'INFO'})
                else:
                     self.q.put({'type': 'log', 'message': f"  在 [{tab_name}] 中未找到任何 site-box-item。", 'level': 'WARNING'})
                return []
            for item in items:
                button_text_element = item.find(class_='van-button__text')
                badge_element = item.find(class_='badge')
                if button_text_element and badge_element:
                    raw_site_name = button_text_element.get_text(strip=True)
                    badge_value_str = badge_element.get_text(strip=True)
                    is_count_zero = False
                    try:
                        badge_value_int = int(badge_value_str)
                        if badge_value_int == 0:
                            is_count_zero = True
                    except ValueError:
                         self.q.put({'type': 'log', 'message': f"  警告: 地點 [{raw_site_name}] 的人數 '{badge_value_str}' 無法解析為數字。", 'level': 'WARNING'})
                         pass
                    combined_site_name = f"{raw_site_name} ({badge_value_str})"
                    self.q.put({'type': 'log', 'message': f'  所屬地點: {combined_site_name}', 'level': 'INFO'})
                    site_data_for_processing.append((raw_site_name, combined_site_name, is_count_zero))
                self._check_stop()
            return site_data_for_processing
        except InterruptedError:
             raise
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"錯誤: 提取 [{tab_name}] 數據時發生錯誤: {e}", 'level': 'ERROR'})
            return []

    # [保留不變] click_tab_by_name
    def click_tab_by_name(self, tab_name, is_erc_tab=False, take_screenshot=True):
        self._check_stop()
        self.q.put({'type': 'log', 'message': f"\n--- 嘗試切換到「{tab_name}」標籤 ({'ERC部門' if is_erc_tab else '主要'}) ---", 'level': 'INFO'})

        # 截圖：切換標籤前 (可選)
        if take_screenshot:
            self._take_screenshot("before_click_tab", f"切換到{tab_name}標籤前")

        try:
            tab_xpath = f"//div[@role='tab']//span[contains(@class,'van-tab__text') and normalize-space()='{tab_name}']/parent::div"
            tab_element = WebDriverWait(self.driver, 20).until(EC.element_to_be_clickable((By.XPATH, tab_xpath)))
            self._check_stop()
            self.driver.execute_script("arguments[0].scrollIntoView();", tab_element)
            time.sleep(0.3)
            self._check_stop()
            tab_element.click()
            time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入
            self.q.put({'type': 'log', 'message': f"成功點擊「{tab_name}」標籤。", 'level': 'INFO'})
            self.q.put({'type': 'log', 'message': f"等待「{tab_name}」標籤內容載入...", 'level': 'INFO'})
            if is_erc_tab:
                content_loaded_xpath = ("//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]" "//div[contains(@class, 'site-box-item') or contains(@class, 'dept-evacuation-details') or contains(@class, 'count-title')]"
)
            else:
                content_loaded_xpath = "//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]//div[contains(@class, 'site-box-item')]"
            WebDriverWait(self.driver, self.config.get('element_timeout_long', 10)).until(EC.presence_of_element_located((By.XPATH, content_loaded_xpath)))
            self._check_stop()
            self.q.put({'type': 'log', 'message': f"「{tab_name}」標籤內容載入完成。", 'level': 'INFO'})

            # 截圖：標籤內容載入完成 (可選)
            if take_screenshot:
                self._take_screenshot("tab_content_loaded", f"{tab_name}標籤內容載入完成")

            time.sleep(self.config['action_pause'])
            self._check_stop()
            return True
        except InterruptedError:
             raise
        except TimeoutException:
            self.q.put({'type': 'log', 'message': f"錯誤: 處理ERC頁面超時，可能未完全載入。", 'level': 'ERROR'})
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"錯誤: 處理ERC頁面時發生未預期錯誤: {e}", 'level': 'ERROR'})

        self.q.put({'type': 'log', 'message': f"--- 完成「{parent_site_name}」的ERC詳細頁面檢查。 ---", 'level': 'INFO'})

    def _handle_webdriver_crash(self, site_name):
        """處理 WebDriver 崩潰的恢復機制"""
        try:
            self.q.put({'type': 'log', 'message': f"檢測到 WebDriver 崩潰，嘗試重新啟動...", 'level': 'WARNING'})
            
            # 關閉當前 WebDriver
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
            
            # 重新啟動 WebDriver
            self._initialize_webdriver()
            
            # 重新登入
            self._login()
            
            return True
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"WebDriver 恢復失敗: {e}", 'level': 'ERROR'})
            return False
            return False
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"錯誤: 切換標籤「{tab_name}」時發生未預期錯誤: {e}", 'level': 'ERROR'})
            return False

    # ========================================================
    # ====== 重大修改: 重構 click_and_check_site_details ======
    # ======           為 _process_single_site          ======
    # ========================================================
    def _process_single_site(self, tab_name, raw_site_name):
        """
        處理單一指定的地點：點擊按鈕，檢查詳細頁面，執行ERC流程等。
        """
        self._check_stop()

        # 從頁面獲取該地點的完整資訊 (含人數)
        combined_site_name = raw_site_name # 預設值
        is_count_zero = False
        try:
            site_button_text_xpath = (
                f"//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]"
                f"//span[contains(@class, 'van-button__text') and normalize-space()='{raw_site_name}']"
            )
            site_button_text_element = self.driver.find_element(By.XPATH, site_button_text_xpath)
            site_item_element = site_button_text_element.find_element(By.XPATH, "./ancestor::div[@class='site-box-item']")
            badge_element = site_item_element.find_element(By.CLASS_NAME, 'badge')
            badge_value_str = badge_element.text.strip()
            combined_site_name = f"{raw_site_name} ({badge_value_str})"
            if int(badge_value_str) == 0:
                is_count_zero = True
        except (NoSuchElementException, ValueError):
            self.q.put({'type': 'log', 'message': f"警告: 無法獲取地點「{raw_site_name}」的完整人數信息。", 'level': 'WARNING'})

        self.q.put({'type': 'log', 'message': f"\n處理地點: {combined_site_name}...", 'level': 'INFO'})
        check_result_for_table = "未處理"

        # 人數為0的警告
        count_warning_message = ""
        if is_count_zero:
            count_warning_message = "警告: 人數為 0"
            self.q.put({'type': 'log', 'message': f"  地點 [{combined_site_name}] 人數為 0。", 'level': 'WARNING'})

        # 截圖：點擊地點前
        self._take_screenshot("before_click_site", f"點擊{combined_site_name}前")

        try:
            site_button_xpath = (
                f"//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]"
                f"//span[contains(@class, 'van-button__text') and normalize-space()='{raw_site_name}']"
                f"/ancestor::button"
            )
            site_button = WebDriverWait(self.driver, self.config['element_timeout_medium']).until(EC.element_to_be_clickable((By.XPATH, site_button_xpath)))
            self._check_stop()
            self.driver.execute_script("arguments[0].scrollIntoView();", site_button)
            time.sleep(0.3)
            self._check_stop()
            site_button.click()
            time.sleep(self.config['page_transition_pause'])  # 增加等待時間讓網頁完全載入
            self.q.put({'type': 'log', 'message': f"成功點擊地點「{combined_site_name}」。", 'level': 'INFO'})
            self.q.put({'type': 'log', 'message': f"等待地點「{combined_site_name}」詳細頁面載入...", 'level': 'INFO'})
            time.sleep(self.config['action_pause'])
            self._check_stop()

            # 截圖：地點詳細頁面載入完成
            self._take_screenshot("site_detail_loaded", f"{combined_site_name}詳細頁面載入完成")

            # 檢測地點詳細頁面載入異常
            detail_page_anomalies = self._check_page_loading_anomalies(f"地點詳細頁面({combined_site_name})")
            self._report_anomalies(detail_page_anomalies, combined_site_name)

            # --- 後續的詳細頁面處理邏輯 (與舊方法 click_and_check_site_details 內部邏輯相同) ---
            page_content_result = self.check_page_content(f"{tab_name} - {combined_site_name} 詳細頁")
            self._check_stop()

            # 截圖：頁面內容檢查完成
            self._take_screenshot("page_content_checked", f"{combined_site_name}頁面內容檢查完成")

            self.q.put({
                'type': 'main_site_result', 'main_tab': tab_name, 'main_site_name': raw_site_name,
                'main_site_total_people': combined_site_name.split(' (')[1].strip(')'),
                'detailed_check_result': page_content_result if not is_count_zero else f"{page_content_result} ({count_warning_message.replace('警告: ', '')})"
            })
            # --- 判斷頁面狀態並執行相應操作 ---
            page_source = self.driver.page_source
            erc_flow_triggered = False

            if START_TEXT in page_source:
                self.q.put({'type': 'log', 'message': f"  在地點「{combined_site_name}」詳細頁面檢測到「{START_TEXT}」按鈕。", 'level': 'WARNING'})
                # 截圖：偵測到疏散按鈕
                self._take_screenshot("evacuation_button_detected", f"{combined_site_name}偵測到疏散按鈕")
                try:
                    # 點擊「開始疏散」按鈕
                    start_evac_button_xpath = f"//button[contains(., '{START_TEXT}')]"
                    start_evac_button = WebDriverWait(self.driver, self.config['element_timeout_short']).until(
                        EC.element_to_be_clickable((By.XPATH, start_evac_button_xpath))
                    )
                    self.driver.execute_script("arguments[0].scrollIntoView();", start_evac_button)
                    time.sleep(0.3)
                    start_evac_button.click()
                    time.sleep(self.config['page_transition_pause'])  # 增加等待時間讓網頁完全載入
                    self.q.put({'type': 'log', 'message': f"  成功點擊「{START_TEXT}」按鈕。", 'level': 'INFO'})

                    # 檢測疏散按鈕異常（是否自動跳回）
                    evacuation_anomalies = self._check_evacuation_button_anomalies(combined_site_name)
                    self._report_anomalies(evacuation_anomalies, combined_site_name)

                    time.sleep(self.config['page_transition_pause']) # 額外等待時間，讓「結束疏散」和「ERC查看該廠疏散」按鈕出現
                    self._check_stop()
                    check_result_for_table = "已觸發開始疏散"
                    erc_flow_triggered = True # 標記為已觸發ERC流程，以便後續點擊ERC查看

                except TimeoutException:
                    self.q.put({'type': 'log', 'message': f"錯誤: 點擊「{START_TEXT}」按鈕超時。跳過該地點的ERC處理。", 'level': 'ERROR'})
                    check_result_for_table = "開始疏散按鈕點擊失敗"
                except Exception as e:
                    self.q.put({'type': 'log', 'message': f"錯誤: 點擊「{START_TEXT}」按鈕時發生未預期錯誤: {e}。跳過該地點的ERC處理。", 'level': 'ERROR'})
                    check_result_for_table = f"開始疏散按鈕點擊錯誤: {e}"

            # 如果 ERC 流程被觸發（點擊了開始疏散按鈕），則執行 ERC 查看和復歸邏輯
            if erc_flow_triggered or FORBIDDEN_TEXT in self.driver.page_source:
                if not erc_flow_triggered:
                    self.q.put({'type': 'log', 'message': f"  在地點「{combined_site_name}」詳細頁面檢測到「{FORBIDDEN_TEXT}」按鈕，觸發ERC頁面檢查流程。", 'level': 'WARNING'})
                    erc_flow_triggered = True # 確保標記為已觸發ERC流程

                try:
                    # 查找並點擊「ERC查看該廠疏散」按鈕
                    self.q.put({'type': 'log', 'message': f"  尋找並點擊「ERC查看該廠疏散」按鈕...", 'level': 'INFO'})
                    erc_view_button_xpath = f"//button[contains(., 'ERC查看該廠疏散')]"
                    erc_view_button = WebDriverWait(self.driver, self.config['element_timeout_medium']).until(
                        EC.element_to_be_clickable((By.XPATH, erc_view_button_xpath))
                    )
                    self.driver.execute_script("arguments[0].scrollIntoView();", erc_view_button)
                    time.sleep(0.3)
                    erc_view_button.click()
                    time.sleep(self.config['page_transition_pause'])  # 增加等待時間讓網頁完全載入
                    self.q.put({'type': 'log', 'message': f"  成功點擊「ERC查看該廠疏散」按鈕。", 'level': 'INFO'})
                    self._check_stop()

                    # 呼叫新函數處理ERC頁面
                    self.process_erc_page(combined_site_name, tab_name) # 傳遞主標籤名稱
                    self._check_stop()

                    # --- 返回主列表頁 (經由詳細頁) ---
                    # 1. 點擊「疏散總表」返回地點詳細頁面
                    self.q.put({'type': 'log', 'message': f"  完成ERC頁面處理，嘗試點擊「疏散總表」返回地點詳細頁。", 'level': 'INFO'})
                    back_to_detail_button_xpath = "//div[contains(@class, 'van-nav-bar__left')]//span[contains(@class, 'van-nav-bar__text') and normalize-space()='疏散總表']"

                    # 等待可能的Toast/Loading提示消失
                    try:
                        self.q.put({'type': 'log', 'message': "  等待可能的Toast/Loading提示消失...", 'level': 'INFO'})
                        WebDriverWait(self.driver, self.config['element_timeout_short']).until_not(
                            EC.presence_of_element_located((By.CSS_SELECTOR, 'body.van-toast--unclickable'))
                        )
                        self.q.put({'type': 'log', 'message': "  Toast/Loading提示已消失。", 'level': 'INFO'})
                    except TimeoutException:
                        self.q.put({'type': 'log', 'message': "  警告: 等待Toast/Loading提示消失超時，嘗試繼續點擊。", 'level': 'WARNING'})

                    back_button_detail = WebDriverWait(self.driver, self.config['element_timeout_short']).until(
                        EC.element_to_be_clickable((By.XPATH, back_to_detail_button_xpath))
                    )
                    self.driver.execute_script("arguments[0].scrollIntoView();", back_button_detail)
                    time.sleep(0.3)
                    back_button_detail.click()
                    time.sleep(self.config.get('page_transition_pause', 3))  # 增加等待時間讓網頁完全載入


                    self.q.put({'type': 'log', 'message': f"  已成功返回地點詳細頁。", 'level': 'INFO'})
                    time.sleep(self.config['wait_time']) # 給頁面充分時間載入列表數據
                    self._check_stop()

                    # 在這裡重新載入頁面
                    self.q.put({'type': 'log', 'message': f"  在點擊「{FORBIDDEN_TEXT}」前重新整理地點詳細頁面。", 'level': 'INFO'})
                    self.driver.refresh()
                    time.sleep(self.config['page_transition_pause']) # 重新整理後等待頁面載入
                    self.q.put({'type': 'log', 'message': f"  在點擊「{FORBIDDEN_TEXT}」前再次重新整理地點詳細頁面。", 'level': 'INFO'})
                    self.driver.refresh()

                    # [修改後程式碼]
                    # 流程修改：為了更穩定地執行復歸操作，此處不直接在當前頁面操作。
                    # 而是返回主列表，重新整理，然後再次點擊地點按鈕進入詳細頁，確保頁面狀態穩定。
                    
                    # 1. 從當前的詳細頁面返回到主列表頁面
                    # self.q.put({'type': 'log', 'message': f"  為確保復歸操作穩定，先返回主列表頁面...", 'level': 'INFO'})
                    # # 使用通用的返回按鈕
                    # back_to_main_list_button = WebDriverWait(self.driver, self.config.get('element_timeout_short', 10)).until(
                    #     EC.element_to_be_clickable(self.Locators.GENERIC_BACK_BUTTON)
                    # )
                    # self.driver.execute_script("arguments[0].scrollIntoView();", back_to_main_list_button)
                    # time.sleep(0.3)
                    # back_to_main_list_button.click()
                    try:
                        self._force_navigate_to_main_page()
                    except Exception as e:
                        self.q.put({'type': 'log', 'message': f"事後復歸檢查: 導航回主頁失敗: {e}", 'level': 'ERROR'})
                        return
                    
                    time.sleep(self.config['reloading_pause'])  # 等待主列表頁面載入

                    # 2. 重新整理主列表頁面
                    self.q.put({'type': 'log', 'message': f"  重新整理主列表頁面以確保元素為最新狀態...", 'level': 'INFO'})
                    self.driver.refresh()
                    time.sleep(self.config['reloading_pause'])  # 等待重新整理完成

                    # 3. 再次點擊原本的地點按鈕，重新進入詳細頁
                    self.q.put({'type': 'log', 'message': f"  重新點擊地點「{raw_site_name}」以準備執行復歸操作...", 'level': 'INFO'})
                    # 重新定位並點擊地點按鈕 (使用與方法開頭相同的邏輯)
                    site_button_xpath_reclick = (
                        f"//div[contains(@class, 'van-tab__panel') and not(contains(@style, 'display: none'))]"
                        f"//span[contains(@class, 'van-button__text') and normalize-space()='{raw_site_name}']"
                        f"/ancestor::button"
                    )
                    site_button_reclick = WebDriverWait(self.driver, self.config['element_timeout_medium']).until(
                        EC.element_to_be_clickable((By.XPATH, site_button_xpath_reclick))
                    )
                    self.driver.execute_script("arguments[0].scrollIntoView();", site_button_reclick)
                    time.sleep(self.config['action_pause'])
                    site_button_reclick.click()
                    
                    # 4. 等待詳細頁面再次載入，並檢查停止旗標
                    self.q.put({'type': 'log', 'message': f"  已重新進入「{raw_site_name}」詳細頁面。", 'level': 'INFO'})
                    time.sleep(self.config['page_transition_pause'])  # 給詳細頁面充分的載入時間
                    self._check_stop()

                    # 2. 在地點詳細頁點擊「結束疏散」按鈕復歸
                    self.q.put({'type': 'log', 'message': f"  在詳細頁面嘗試點擊「{FORBIDDEN_TEXT}」按鈕進行復歸。", 'level': 'INFO'})
                    end_evac_button_xpath = f"//button[contains(., '{FORBIDDEN_TEXT}')]"
                    end_evac_button = WebDriverWait(self.driver, self.config['element_timeout_short']).until(
                        EC.element_to_be_clickable((By.XPATH, end_evac_button_xpath))
                    )

                    time.sleep(self.config['page_transition_pause']) # 重新整理後等待頁面載入
                    self._check_stop()

                    # 2. 在地點詳細頁點擊「結束疏散」按鈕復歸
                    self.q.put({'type': 'log', 'message': f"  在詳細頁面嘗試點擊「{FORBIDDEN_TEXT}」按鈕進行復歸。", 'level': 'INFO'})
                    end_evac_button_xpath = f"//button[contains(., '{FORBIDDEN_TEXT}')]"
                    end_evac_button = WebDriverWait(self.driver, self.config['element_timeout_short']).until(
                        EC.element_to_be_clickable((By.XPATH, end_evac_button_xpath))
                    )
                    
                    self.driver.execute_script("arguments[0].scrollIntoView();", end_evac_button)
                    time.sleep(0.3)
                    end_evac_button.click()
                    time.sleep(self.config['page_transition_pause'])  # 增加等待時間讓網頁完全載入
                    self.q.put({'type': 'log', 'message': f"  成功點擊「{FORBIDDEN_TEXT}」按鈕，完成復歸並返回主列表頁。", 'level': 'INFO'})
                    time.sleep(self.config['reloading_pause']) # 等待返回主列表
                    self._check_stop()

                    check_result_for_table = "已處理ERC頁並復歸"
                    time.sleep(self.config['wait_time']) # 等待返回主列表

                except TimeoutException:
                    self.q.put({'type': 'log', 'message': f"錯誤: ERC處理或返回主列表超時。此廠區後續地點將跳過。", 'level': 'ERROR'})
                    check_result_for_table = "ERC處理或返回失敗"
                    raise # 無法返回主列表，後續地點會失敗，所以直接拋出異常讓外層 Tab 循環結束
                except Exception as erc_e:
                    self.q.put({'type': 'log', 'message': f"錯誤: ERC按鈕或頁面處理失敗，或返回主列表時發生未預期錯誤: {erc_e}。此廠區後續地點將跳過。", 'level': 'ERROR'})
                    check_result_for_table = f"ERC處理錯誤或返回失敗: {erc_e}"
                    raise # 無法返回主列表，後續地點會失敗，所以直接拋出異常讓外層 Tab 循環結束

            if not erc_flow_triggered: # 如果沒有觸發任何 ERC 相關流程，則點擊通用返回按鈕
                self.q.put({'type': 'log', 'message': f"  地點「{combined_site_name}」無ERC處理流程。嘗試點擊通用返回按鈕。", 'level': 'INFO'})
                try:
                    # 查找並點擊通用返回按鈕（通常是左上角的箭頭）
                    generic_back_button_xpath = "//div[contains(@class, 'van-nav-bar__left')]"
                    back_button_general = WebDriverWait(self.driver, self.config['element_timeout_short']).until(
                        EC.element_to_be_clickable((By.XPATH, generic_back_button_xpath))
                    )
                    self.driver.execute_script("arguments[0].scrollIntoView();", back_button_general)
                    time.sleep(0.3)
                    back_button_general.click()
                    time.sleep(self.config['page_transition_pause'])  # 增加等待時間讓網頁完全載入
                    self.q.put({'type': 'log', 'message': f"  已點擊通用返回按鈕，返回主列表頁。", 'level': 'INFO'})
                    time.sleep(self.config['wait_time']) # 給頁面充分時間載入主列表
                    self._check_stop()
                    if check_result_for_table == "未處理": # 如果之前沒有被ERC流程更新過狀態
                        check_result_for_table = "檢查完成並返回"
                        if is_count_zero:
                            check_result_for_table += f" ({count_warning_message.replace('警告: ', '')})"

                except TimeoutException:
                    self.q.put({'type': 'log', 'message': f"錯誤: 點擊通用返回按鈕超時。可能卡在詳細頁面。此廠區後續地點將跳過。", 'level': 'ERROR'})
                    check_result_for_table = "通用返回失敗"
                    raise # 無法返回主列表，後續地點會失敗
                except Exception as general_back_e:
                    self.q.put({'type': 'log', 'message': f"錯誤: 點擊通用返回按鈕時發生未預期錯誤: {general_back_e}。此廠區後續地點將跳過。", 'level': 'ERROR'})
                    check_result_for_table = f"通用返回錯誤: {general_back_e}"
                    raise # 無法返回主列表，後續地點會失敗

            time.sleep(self.config['action_pause']) # 在下一個地點檢查前稍作等待
            self._check_stop()

            # 返回 True 表示成功處理並返回到列表頁
            return True

        except InterruptedError:
            raise
        except (TimeoutException, StaleElementReferenceException, Exception) as e:
            error_msg = f"處理地點「{combined_site_name}」時發生錯誤: {e}"
            self.q.put({'type': 'log', 'message': error_msg, 'level': 'ERROR'})
            check_result_for_table = f"處理錯誤"
            # 發生錯誤，嘗試返回主列表頁面以繼續處理下一個地點
            self._attempt_to_return_to_main_list(f"處理「{combined_site_name}」錯誤後")
            return False # 表示處理失敗
        finally:
            self.q.put({'type': 'ui_update', 'tab': tab_name, 'site': combined_site_name, 'result': check_result_for_table})

    def _finalize_session_and_merge_images(self):
        """完成會話並合併圖片"""
        try:
            if self.current_session_folder and os.path.exists(self.current_session_folder):
                # 合併所有截圖
                self.q.put({'type': 'log', 'message': "正在合併截圖...", 'level': 'INFO'})
                success = self.image_merger.merge_screenshots_to_output(self.current_session_folder)

                if success:
                    self.q.put({'type': 'log', 'message': "✅ 截圖合併完成，已保存到 pic_output 資料夾", 'level': 'INFO'})
                else:
                    self.q.put({'type': 'log', 'message': "❌ 截圖合併失敗", 'level': 'WARNING'})

                return success
            else:
                self.q.put({'type': 'log', 'message': "沒有找到截圖資料夾，跳過合併", 'level': 'INFO'})
                return False

        except Exception as e:
            self.q.put({'type': 'log', 'message': f"合併截圖時發生錯誤: {e}", 'level': 'ERROR'})
            return False

    def _process_site_with_timeout(self, raw_site_name, combined_site_name, is_count_zero, tab_name, timeout=300):
        """
        帶超時的地點處理（Windows 兼容版本）
        """
        import threading
        import time

        result = {'success': False, 'error': None}

        def target():
            try:
                result['success'] = self._process_single_site(raw_site_name, combined_site_name, is_count_zero, tab_name)
            except Exception as e:
                result['error'] = e

        # 創建並啟動線程
        thread = threading.Thread(target=target)
        thread.daemon = True
        thread.start()

        # 等待線程完成或超時
        thread.join(timeout)

        if thread.is_alive():
            # 超時處理
            self.q.put({'type': 'log', 'message': f"⏰ 處理地點 {combined_site_name} 超時", 'level': 'WARNING'})
            # 嘗試恢復
            try:
                self._attempt_to_return_to_main_list(f"處理「{combined_site_name}」超時後")
            except:
                pass
            return False
        elif result['error']:
            # 執行錯誤
            self.q.put({'type': 'log', 'message': f"處理地點 {combined_site_name} 時發生錯誤: {result['error']}", 'level': 'ERROR'})
            return False
        else:
            # 成功完成
            return result['success']

    # [保留不變] _post_cycle_reversion_check & _attempt_to_return_to_main_list & _force_navigate_to_main_page
    # 但 _post_cycle_reversion_check 需接收 selected_sites 參數
    def _post_cycle_reversion_check_selected(self, selected_sites):
        self._check_stop()
        self.q.put({'type': 'log', 'message': "\n--- 開始執行事後復歸檢查 (僅檢查已選地點) ---", 'level': 'INFO'})
        
        try:
            self._force_navigate_to_main_page()
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"事後復歸檢查: 導航回主頁失敗: {e}", 'level': 'ERROR'})
            return

        current_tab = None
        for site_name in selected_sites:
            self._check_stop()
            target_tab = SITE_TO_TAB_MAP.get(site_name)
            if not target_tab:
                self.q.put({'type': 'log', 'message': f"事後復歸檢查: 找不到地點 {site_name} 的所屬標籤。", 'level': 'WARNING'})
                continue

            if target_tab != current_tab:
                self.q.put({'type': 'log', 'message': f"\n事後復歸檢查: 切換到「{target_tab}」標籤...", 'level': 'INFO'})
                if not self.click_tab_by_name(target_tab):
                    self.q.put({'type': 'log', 'message': f"事後復歸檢查: 切換到「{target_tab}」失敗，跳過此標籤的復歸檢查。", 'level': 'ERROR'})
                    current_tab = None # 標記為未知，下次強制切換
                    continue
                current_tab = target_tab
            
            # ... (後續的單個地點檢查邏輯與舊方法類似，但目標是 site_name) ...
            try:
                # 點擊地點，檢查，復歸，返回
                pass
            except Exception as e:
                self.q.put({'type': 'log', 'message': f"事後復歸檢查: 處理地點 {site_name} 出錯: {e}", 'level': 'ERROR'})
                self._attempt_to_return_to_main_list(f"檢查 {site_name} 錯誤後")


    def _attempt_to_return_to_main_list(self, reason=""):
        self._check_stop()
        try:
            self.q.put({'type': 'log', 'message': f"  {reason} 嘗試點擊通用返回按鈕返回主列表頁。", 'level': 'INFO'})
            generic_back_button_xpath = "//div[contains(@class, 'van-nav-bar__left')]"
            back_button_general = WebDriverWait(self.driver, self.config['element_timeout_short']).until(EC.element_to_be_clickable((By.XPATH, generic_back_button_xpath)))
            self.driver.execute_script("arguments[0].scrollIntoView();", back_button_general)
            time.sleep(0.3)
            back_button_general.click()
            time.sleep(self.config['page_transition_pause'])  # 增加等待時間讓網頁完全載入
            self.q.put({'type': 'log', 'message': f"  {reason} 已成功點擊通用返回按鈕。", 'level': 'INFO'})
            time.sleep(self.config['wait_time'])
            self._check_stop()
        except Exception:
            self.q.put({'type': 'log', 'message': f"  {reason} 點擊通用返回按鈕失敗。嘗試強制導航回主頁。", 'level': 'ERROR'})
            self._force_navigate_to_main_page()

    def _force_navigate_to_main_page(self):
        self._check_stop()
        try:
            self.q.put({'type': 'log', 'message': "  強制導航回主頁面...", 'level': 'INFO'})
            self.driver.get(self.config['url'])
            WebDriverWait(self.driver, self.config['element_timeout_long']).until(EC.presence_of_element_located((By.CLASS_NAME, 'site-box-item')))
            self.q.put({'type': 'log', 'message': "  已成功強制導航回主頁面。", 'level': 'INFO'})
            time.sleep(self.config['action_pause'])
        except Exception as e:
            self.q.put({'type': 'log', 'message': f"  強制導航回主頁面失敗: {e}。", 'level': 'ERROR'})
            raise
            
    # ========================================================
    # ====== (合併修正版) 核心自動化流程 run_automation_cycle ======
    # ========================================================
    def _log_message(self, message, level='INFO'):
        """統一的日誌記錄方法，同時發送到UI和寫入檔案"""
        self.q.put({'type': 'log', 'message': message, 'level': level})
        self._write_to_log_file(message, level)

    def run_automation_cycle(self, selected_sites):
        """
        (合併修正版) 根據傳入的已選擇地點列表，執行自動化流程。
        本版本整合了經過驗證的登入邏輯和按需執行的地點處理邏輯。
        """
        self.driver = None
        self.status.start_cycle()
        cycle_start_time = time.strftime('%Y-%m-%d %H:%M:%S')
        self._log_message(f"\n--- 開始新的自動化週期 (開始時間: {cycle_start_time}) ---", 'INFO')
        self._log_message(f"將處理以下已選地點: {selected_sites}", 'INFO')

        # 創建本次執行的專用資料夾
        self._create_session_folder()

        try:
            # --- 啟動 WebDriver (優化版) ---
            chrome_options = webdriver.ChromeOptions()

            # 基本性能優化
            chrome_options.add_argument('--disable-gpu')
            chrome_options.add_argument('--disable-dev-shm-usage')
            chrome_options.add_argument('--no-sandbox')
            chrome_options.add_argument('--dns-prefetch-disable')

            # 加速網頁載入
            chrome_options.add_argument('--disable-images')  # 禁用圖片載入
            # chrome_options.add_argument('--disable-javascript')  # 如果網站允許，禁用JS (註解掉以防網頁功能異常)
            chrome_options.add_argument('--disable-plugins')  # 禁用插件
            chrome_options.add_argument('--disable-extensions')  # 禁用擴展
            chrome_options.add_argument('--disable-web-security')  # 禁用網頁安全檢查
            chrome_options.add_argument('--disable-features=TranslateUI')  # 禁用翻譯
            chrome_options.add_argument('--disable-ipc-flooding-protection')  # 禁用IPC洪水保護
            chrome_options.add_argument('--disable-renderer-backgrounding')  # 禁用渲染器背景化
            chrome_options.add_argument('--disable-backgrounding-occluded-windows')  # 禁用背景窗口
            chrome_options.add_argument('--disable-client-side-phishing-detection')  # 禁用釣魚檢測
            chrome_options.add_argument('--disable-sync')  # 禁用同步
            chrome_options.add_argument('--disable-default-apps')  # 禁用默認應用
            chrome_options.add_argument('--no-first-run')  # 跳過首次運行
            chrome_options.add_argument('--no-default-browser-check')  # 跳過默認瀏覽器檢查
            chrome_options.add_argument('--disable-logging')  # 禁用日誌
            chrome_options.add_argument('--disable-log-file')  # 禁用日誌文件

            # 設置頁面載入策略為 eager (DOM載入完成即可，不等待所有資源)
            chrome_options.page_load_strategy = 'eager'

            # 設置用戶代理以避免某些檢測
            chrome_options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

            # chrome_options.add_argument('--headless')  # 如需無頭模式可取消註解

            # 開始整體登入流程計時
            self.login_start_time = time.time()
            self.q.put({'type': 'log', 'message': "🚀 開始登入流程時間測試", 'level': 'INFO'})

            # 階段1: WebDriver 啟動
            self._start_timing("1. WebDriver啟動")
            self.q.put({'type': 'log', 'message': "正在啟動 WebDriver...", 'level': 'INFO'})
            service = Service(self.config['driver_path'])
            self.driver = webdriver.Chrome(service=service, options=chrome_options)
            self.driver.set_window_size(self.config['window_width'], self.config['window_height'])
            self.q.put({'type': 'log', 'message': "WebDriver 初始化成功。", 'level': 'INFO'})

            # 初始化登入處理器
            self.login_handler = LoginHandler(self.driver, self.config, self.q, self._take_screenshot)
            time.sleep(self.config['initial_load_pause'])
            self._end_timing("1. WebDriver啟動")

            # 截圖：WebDriver 啟動完成
            # self._take_screenshot("webdriver_started", "WebDriver啟動完成")
            self._check_stop()

            # 階段2: 網頁導航
            self._start_timing("2. 網頁導航")
            self.q.put({'type': 'log', 'message': f"導航到 {self.config['url']}", 'level': 'INFO'})
            self.driver.get(self.config['url'])
            wait = WebDriverWait(self.driver, self.config['element_timeout_long'])  # 增加等待時間
            self._check_stop()
            self._end_timing("2. 網頁導航")

            # 階段3: 登入頁面載入
            self._start_timing("3. 登入頁面載入")
            self.q.put({'type': 'log', 'message': "智能等待頁面載入...", 'level': 'INFO'})
            try:
                # 等待登入表單出現，而不是固定等待時間
                WebDriverWait(self.driver, self.config['element_timeout_medium']).until(
                    EC.presence_of_element_located((By.ID, 'txtAccount_E'))
                )
                self.q.put({'type': 'log', 'message': "登入表單已載入", 'level': 'INFO'})
            except TimeoutException:
                self.q.put({'type': 'log', 'message': "等待登入表單超時，使用備用等待", 'level': 'WARNING'})
                time.sleep(3)  # 備用等待時間，比原來短
            self._end_timing("3. 登入頁面載入")

            # 快速模式：條件性截圖
            if self.config['skip_login_screenshots'].lower() != 'true':
                self._take_screenshot("login_page_loaded", "載入登入頁面")

            # 使用新的登入處理器執行登入流程
            try:
                self._check_stop()

                # 階段4: 帳號和身分證輸入
                self._start_timing("4. 帳號和身分證輸入")
                if not self.login_handler.fast_sequential_input():
                    raise Exception("快速連續輸入失敗")
                self._end_timing("4. 帳號和身分證輸入")

                # 快速模式：減少等待時間
                time.sleep(self.login_handler.login_config['login_transition_wait'])
                self._check_stop()

                # 階段5: 密碼輸入和登入
                self._start_timing("5. 密碼輸入和登入")
                if not self.login_handler.fast_password_input_and_login():
                    raise Exception("快速密碼輸入和登入失敗")
                self._end_timing("5. 密碼輸入和登入")

            except TimeoutException:
                 self.q.put({'type': 'log', 'message': "錯誤: 登入流程中，等待輸入框或登入按鈕超時。請檢查登入流程或頁面加載是否正常。", 'level': 'ERROR'})
                 raise
            except InterruptedError:
                 raise
            except Exception as e:
                 # 截圖：登入流程發生錯誤
                 self._take_screenshot("login_error", f"登入流程發生錯誤")
                 self.q.put({'type': 'log', 'message': f"錯誤: 登入流程發生未預期錯誤: {e}", 'level': 'ERROR'})
                 raise
            
            self.q.put({'type': 'log', 'message': "--- 登入流程結束 ---", 'level': 'INFO'})

            # 階段6: 主畫面載入
            self._start_timing("6. 主畫面載入")
            self.q.put({'type': 'log', 'message': "\n--- 開始根據選擇的地點進行處理 ---", 'level': 'INFO'})
            
            # ============================================
            # ====== 修改處: 在此呼叫監控方法 ======
            # ============================================
            # --- 新增：監控主頁面載入動畫時間 ---
            # 這個時間點是登入成功後，主應用頁面最可能出現載入動畫的地方
            # 使用專門的初次載入超時參數
            self.monitor_loading_animation(timeout_sec=self.config['first_loading_timeout_sec'])
            self._check_stop()
            # ============================================
            
            # 等待登入後的主頁面元素出現
            WebDriverWait(self.driver, self.config['element_timeout_long_site']).until(EC.presence_of_element_located((By.CLASS_NAME, 'site-box-item')))

            # 截圖：登入成功，主頁面載入完成
            # self._take_screenshot("main_page_loaded", "登入成功主頁面載入完成")

            # 檢測主畫面異常（場域層級）
            main_page_anomalies = self._check_page_loading_anomalies("主畫面(場域)")
            self._report_anomalies(main_page_anomalies, "主畫面")
            self._end_timing("6. 主畫面載入")

            # 計算並記錄總體登入時間
            if self.login_start_time:
                total_login_time = time.time() - self.login_start_time
                if total_login_time < 1:
                    total_time_str = f"{total_login_time*1000:.0f}ms"
                else:
                    total_time_str = f"{total_login_time:.2f}s"
                self.q.put({'type': 'log', 'message': f"🎉 完整登入流程總耗時: {total_time_str}", 'level': 'INFO'})

                # 記錄總時間到timing_records
                self.timing_records["總計時間"] = total_login_time

            # 輸出時間統計總結
            self._log_timing_summary()

            # 檢測場域是否缺少或重複
            self._check_main_page_tabs_anomalies()

            # 檢測功能列異常
            function_anomalies = self._check_function_list_anomalies()
            self._report_anomalies(function_anomalies, "功能列")

            current_tab = None # 用於追蹤當前所在的 Tab，避免重複點擊

            for site_name in selected_sites:
                self._check_stop()

                target_tab = SITE_TO_TAB_MAP.get(site_name)
                if not target_tab:
                    self.q.put({'type': 'log', 'message': f"錯誤: 在地點地圖中找不到「{site_name}」，跳過。", 'level': 'ERROR'})
                    continue

                # 如果目標 Tab 不是當前 Tab，則點擊切換
                if target_tab != current_tab:
                    if self.click_tab_by_name(target_tab):
                        current_tab = target_tab
                        self.q.put({'type': 'log', 'message': f"當前標籤已切換為: {current_tab}", 'level': 'INFO'})
                        time.sleep(self.config['action_pause']) # 切換後等待一下

                        # 檢測當前標籤下的站點數據異常
                        try:
                            sites_info = self.extract_and_display_data(current_tab)
                            sites_data = {}
                            for raw_site_name, combined_site_name, is_count_zero in sites_info:
                                # 從 combined_site_name 中提取人數
                                import re
                                match = re.search(r'\((\d+)\)', combined_site_name)
                                count = int(match.group(1)) if match else 0
                                sites_data[raw_site_name] = count

                            # 執行站點數據異常檢測
                            site_anomalies = self._check_site_data_anomalies(current_tab, sites_data)
                            self._report_anomalies(site_anomalies, f"{current_tab}下層")
                        except Exception as e:
                            self.q.put({'type': 'log', 'message': f"檢測「{current_tab}」標籤站點數據異常時發生錯誤: {e}", 'level': 'WARNING'})
                    else:
                        self.q.put({'type': 'log', 'message': f"切換到「{target_tab}」失敗，跳過地點「{site_name}」。", 'level': 'ERROR'})
                        current_tab = None # 標記為未知，下次強制切換
                        continue
                
                # 在正確的 Tab 下，處理單一地點
                self._process_single_site(current_tab, site_name)

                if not self.status.is_stop_requested():
                    if not interruptible_sleep(self.config['wait_time'], self.status, self.q, reason=f"地點間等待 ({site_name} -> 下一個)"):
                         raise InterruptedError("地點間等待被中斷")
            
            self.q.put({'type': 'log', 'message': "\n--- 所有已選地點處理完成。 ---", 'level': 'INFO'})

            # 截圖：所有地點處理完成
            # self._take_screenshot("all_sites_completed", "所有地點處理完成")

        except InterruptedError:
            self.q.put({'type': 'log', 'message': "\n自動化週期被停止請求中斷。", 'level': 'WARNING'})
        except TimeoutException as e:
            self.q.put({'type': 'log', 'message': f"\n自動化週期因 Timeout 錯誤終止: {e}", 'level': 'ERROR'})
        except NoSuchElementException as e:
            self.q.put({'type': 'log', 'message': f"\n自動化週期因找不到元素錯誤終止: {e}", 'level': 'ERROR'})
        except StaleElementReferenceException as e:
            self.q.put({'type': 'log', 'message': f"\n自動化週期因元素過時錯誤終止: {e}", 'level': 'ERROR'})
        except WebDriverException as e:
             self.q.put({'type': 'log', 'message': f"\n自動化週期因 WebDriver 錯誤終止: {e}", 'level': 'ERROR'})
        except Exception as e:
            # 截圖：自動化週期發生錯誤
            self._take_screenshot("automation_cycle_error", f"自動化週期發生錯誤")
            self.q.put({'type': 'log', 'message': f"\n自動化週期出錯終止: {e}", 'level': 'ERROR'})
            
        finally:
            # 寫入執行結束日誌
            self._write_to_log_file(f"自動化週期結束時間: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}", 'INFO')

            if self.driver:
                try:
                    self._check_stop() # 在關閉驅動前再次檢查停止旗標
                    # 事後檢查也只檢查本次處理過的地點
                    self._post_cycle_reversion_check_selected(selected_sites)
                except InterruptedError:
                    self._log_message("事後復歸檢查被停止請求中斷。", 'WARNING')
                except Exception as post_e:
                    self._log_message(f"事後復歸檢查發生錯誤: {post_e}", 'ERROR')

                self._log_message("\n正在關閉 WebDriver...", 'INFO')
                try:
                    # 強制關閉所有瀏覽器視窗
                    self.driver.quit()
                    # 等待一段時間確保資源完全釋放
                    time.sleep(self.config['wait_time'])
                except Exception as quit_e:
                     self._log_message(f"關閉 WebDriver 時發生錯誤: {quit_e}", 'ERROR')
                finally:
                    self.driver = None

                self._log_message("WebDriver 已關閉。", 'INFO')
            else:
                 self._log_message("\nWebDriver 未成功啟動或已在週期中關閉。", 'INFO')

            # 重置測試地點追蹤，為下一次執行做準備
            self.wait_confirm_tested_main_sites.clear()
            self._log_message("已重置測試地點追蹤狀態", 'INFO')

            # 先合併截圖（確保通知發送時使用當次執行的圖片）
            self._finalize_session_and_merge_images()

            # 發送 LINK 通知（如果有異常站點）
            if self.link_notification.anomaly_sites:
                self._log_message("檢測到異常站點，準備發送 LINK 通知...", 'INFO')
                if self.link_notification.send_notification():
                    self._log_message("LINK 通知發送成功", 'INFO')
                else:
                    self._log_message("LINK 通知發送失敗", 'ERROR')
                # 清空異常站點列表
                self.link_notification.clear_anomaly_sites()

            # 發送 AUTOMAIL 通知（如果有異常站點）
            if self.automail_notification.anomaly_sites:
                self._log_message("檢測到異常站點，準備發送 AUTOMAIL 通知...", 'INFO')
                if self.automail_notification.send_notification():
                    self._log_message("AUTOMAIL 通知發送成功", 'INFO')
                else:
                    self._log_message("AUTOMAIL 通知發送失敗", 'ERROR')
                # 清空異常站點列表
                self.automail_notification.clear_anomaly_sites()

            self.status.end_cycle()

            # 寫入日誌檔案結束標記
            self._write_to_log_file("=" * 50, 'INFO')
            self._write_to_log_file("自動化週期執行完成", 'INFO')


# ====== 自動化調度執行緒函數 ======
# ... (此部分及之後的 UI 程式碼保持不變) ...
def automation_scheduler_thread(ui_instance, selected_sites_times):
    status = ui_instance.automation_status
    q = ui_instance.message_queue
    config_data = ui_instance.config_data
    selenium_automation = SeleniumAutomation(status, q, config_data)
    status.reset_stop()

    # 記錄排程設定
    q.put({'type': 'log', 'message': f"排程器已啟動", 'level': 'INFO'})
    for site, times in selected_sites_times.items():
        q.put({'type': 'log', 'message': f"地點 {site} 將在以下時間點執行: {times}", 'level': 'INFO'})

    def get_next_scheduled_time():
        """計算下一個排程執行時間，返回 (時間, 該時間需要執行的地點列表)"""
        import datetime
        now = datetime.datetime.now()
        current_hour = now.hour

        # 收集所有可能的執行時間
        all_hours = set()
        for site_times in selected_sites_times.values():
            all_hours.update(site_times)
        all_hours = sorted(list(all_hours))

        # 找到下一個排程時間
        next_hour = None
        for hour in all_hours:
            if hour > current_hour:
                next_hour = hour
                break

        if next_hour is None:
            # 如果今天沒有更多排程時間，則使用明天的第一個時間
            next_hour = all_hours[0] if all_hours else 0
            next_time = now.replace(hour=next_hour, minute=0, second=0, microsecond=0) + datetime.timedelta(days=1)
        else:
            # 使用今天的下一個排程時間
            next_time = now.replace(hour=next_hour, minute=0, second=0, microsecond=0)

        # 找出在這個時間需要執行的地點
        sites_to_run = []
        for site, times in selected_sites_times.items():
            if next_hour in times:
                sites_to_run.append(site)

        return next_time, sites_to_run

    # 等待第一個排程時間
    next_time, sites_to_run = get_next_scheduled_time()
    q.put({'type': 'log', 'message': f"下一次執行時間: {next_time.strftime('%Y-%m-%d %H:%M:%S')}", 'level': 'INFO'})
    q.put({'type': 'log', 'message': f"將執行地點: {sites_to_run}", 'level': 'INFO'})

    while not status.is_stop_requested():
        # 計算等待時間
        now = datetime.datetime.now()
        wait_seconds = (next_time - now).total_seconds()

        if wait_seconds > 0:
            # 等待到排程時間
            q.put({'type': 'log', 'message': f"等待排程時間，剩餘 {int(wait_seconds)} 秒...", 'level': 'INFO'})

            # 使用可中斷的等待
            if not interruptible_sleep(wait_seconds, status, q, reason=f"等待排程時間 ({next_time.strftime('%H:%M')})"):
                q.put({'type': 'log', 'message': "排程等待被中斷", 'level': 'INFO'})
                break

        # 檢查是否被停止
        if status.is_stop_requested():
            break

        # 執行自動化任務
        try:
            q.put({'type': 'log', 'message': f"⏰ 排程時間到達，開始執行自動化任務 ({next_time.strftime('%Y-%m-%d %H:%M:%S')})", 'level': 'INFO'})

            # 重新創建 SeleniumAutomation 實例以避免狀態污染
            selenium_automation = SeleniumAutomation(status, q, config_data)
            selenium_automation.run_automation_cycle(sites_to_run)
            q.put({'type': 'log', 'message': "✅ 排程任務執行完成", 'level': 'INFO'})

        except InterruptedError:
            q.put({'type': 'log', 'message': "⚠️ 排程任務被用戶中斷", 'level': 'WARNING'})
            break
        except Exception as e:
            import traceback
            error_details = traceback.format_exc()
            q.put({'type': 'log', 'message': f"❌ 排程任務執行失敗: {e}", 'level': 'ERROR'})
            q.put({'type': 'log', 'message': f"錯誤詳情: {error_details}", 'level': 'ERROR'})

            # 等待一段時間後繼續下一個排程，避免連續失敗
            q.put({'type': 'log', 'message': "等待 30 秒後繼續下一個排程...", 'level': 'INFO'})
            if not interruptible_sleep(30, status, q, reason="錯誤恢復等待"):
                break

        # 檢查是否被停止
        if status.is_stop_requested():
            break

        # 計算下一個排程時間
        next_time, sites_to_run = get_next_scheduled_time()
        q.put({'type': 'log', 'message': f"下一次執行時間: {next_time.strftime('%Y-%m-%d %H:%M:%S')}", 'level': 'INFO'})
        q.put({'type': 'log', 'message': f"將執行地點: {sites_to_run}", 'level': 'INFO'})

    q.put({'type': 'log', 'message': "排程器已停止", 'level': 'INFO'})

def run_test_cycle_thread(ui_instance, selected_sites):
    status = ui_instance.automation_status
    q = ui_instance.message_queue
    config_data = ui_instance.config_data
    q.put({'type': 'log', 'message': "\n--- 單次測試執行緒啟動 ---", 'level': 'INFO'})
    selenium_automation = SeleniumAutomation(status, q, config_data)
    try:
        selenium_automation.run_automation_cycle(selected_sites)
    except Exception as e:
        q.put({'type': 'log', 'message': f"單次測試執行出錯終止: {e}", 'level': 'ERROR'})
    q.put({'type': 'log', 'message': "單次測試執行緒結束。", 'level': 'INFO'})


# ====== UI 介面類別 ======
class AutomationUI:
    def __init__(self, root):
        style = tb.Style("solar")
        self.root = root
        self.root.title("Web Automation Tool")
        self.root.geometry("900x1200") # 增加高度以容納新元件
        self.root.resizable(True, True)
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 載入配置檔案（必須在使用 config_data 之前）
        try:
            self.config_data = load_config()
        except Exception as e:
            self.config_data = {}
            print(f"載入配置檔案失敗: {e}")

        header = tb.Label(self.root, text="疏散平台自動化監控檢查工具", bootstyle=PRIMARY, font=("Helvetica", 20, "bold"), anchor="center")
        header.pack(fill=X, pady=(15, 5), padx=10)

        # 狀態條
        # ... (此部分不變) ...
        status_frame = tb.Frame(self.root)
        status_frame.pack(fill=X, padx=10, pady=2)
        status_frame.columnconfigure(0, weight=1)
        status_frame.columnconfigure(1, weight=0)
        self.status_var = tb.StringVar(value="待命中")
        statusbar = tb.Label(status_frame, textvariable=self.status_var, bootstyle=INFO, anchor="w", font=("Helvetica", 10))
        statusbar.grid(row=0, column=0, sticky=(W, E))
        self.countdown_var = tb.StringVar(value="")
        self.countdown_label = tb.Label(status_frame, textvariable=self.countdown_var, bootstyle=SECONDARY, anchor="e", font=("Courier", 10))
        self.countdown_label.grid(row=0, column=1, sticky=(W, E))

        # 控制區
        ctrl_frame = tb.LabelFrame(self.root, text="控制面板", bootstyle=PRIMARY)
        ctrl_frame.pack(fill=X, padx=10, pady=5)

        # 第一行：主要控制按鈕
        self.start_button = tb.Button(ctrl_frame, text="開始排程", bootstyle=SUCCESS, command=self.start_scheduler)
        self.start_button.grid(row=0, column=0, padx=6, pady=5, sticky="ew")
        self.test_button = tb.Button(ctrl_frame, text="TEST 立即執行一次", bootstyle=WARNING, command=self.start_test_run)
        self.test_button.grid(row=0, column=1, padx=6, pady=5, sticky="ew")
        self.stop_button = tb.Button(ctrl_frame, text="停止執行", bootstyle=DANGER, command=self.stop_automation, state=DISABLED)
        self.stop_button.grid(row=0, column=2, padx=6, pady=5, sticky="ew")
        self.export_button = tb.Button(ctrl_frame, text="匯出結果 (CSV)", bootstyle=INFO, command=self.export_results_to_csv)
        self.export_button.grid(row=0, column=3, padx=6, pady=5, sticky="ew")

        # 第二行：通知設定
        self.link_notification_var = tk.BooleanVar()
        link_notification_enabled = self.config_data.get('enable_line_notification', 'false').lower() == 'true'
        self.link_notification_var.set(link_notification_enabled)
        self.link_notification_checkbox = tb.Checkbutton(
            ctrl_frame,
            text="啟用 LINK 通知",
            variable=self.link_notification_var,
            bootstyle="success-round-toggle",
            command=self.toggle_link_notification
        )
        self.link_notification_checkbox.grid(row=1, column=0, padx=6, pady=5, sticky="w")

        # AUTOMAIL 通知設定
        self.automail_notification_var = tk.BooleanVar()
        automail_notification_enabled = self.config_data.get('enable_automail', 'false').lower() == 'true'
        self.automail_notification_var.set(automail_notification_enabled)
        self.automail_notification_checkbox = tb.Checkbutton(
            ctrl_frame,
            text="啟用 AUTOMAIL 通知",
            variable=self.automail_notification_var,
            bootstyle="success-round-toggle",
            command=self.toggle_automail_notification
        )
        self.automail_notification_checkbox.grid(row=1, column=1, padx=6, pady=5, sticky="w")
        ctrl_frame.columnconfigure(0, weight=1)
        ctrl_frame.columnconfigure(1, weight=1)
        ctrl_frame.columnconfigure(2, weight=1)
        ctrl_frame.columnconfigure(3, weight=1)

        # =========================================================
        # ====== 新增: 地點選擇 UI 區塊 (使用 ScrolledFrame) ======
        # =========================================================
        selection_frame = tb.LabelFrame(self.root, text="地點選擇與時間設定", bootstyle=SUCCESS)
        selection_frame.pack(fill=X, padx=10, pady=5)

        # 頂部按鈕 (全選/全不選)
        btn_container = tb.Frame(selection_frame)
        btn_container.pack(fill=X, padx=5, pady=5)
        select_all_btn = tb.Button(btn_container, text="全選地點", command=self.select_all_sites, bootstyle="secondary")
        select_all_btn.pack(side=LEFT, padx=5)
        deselect_all_btn = tb.Button(btn_container, text="全不選地點", command=self.deselect_all_sites, bootstyle="secondary")
        deselect_all_btn.pack(side=LEFT, padx=5)
        select_all_times_btn = tb.Button(btn_container, text="全選時間", command=self.select_all_times, bootstyle="info")
        select_all_times_btn.pack(side=LEFT, padx=5)
        deselect_all_times_btn = tb.Button(btn_container, text="全不選時間", command=self.deselect_all_times, bootstyle="info")
        deselect_all_times_btn.pack(side=LEFT, padx=5)

        # 可滾動的複選框區域
        scrolled_content = ScrolledFrame(selection_frame, autohide=True, height=250)
        scrolled_content.pack(fill=BOTH, expand=True, padx=5, pady=5)

        self.site_vars = {} # 用於存放每個地點的 BooleanVar
        self.site_time_vars = {} # 用於存放每個地點的時間選擇 BooleanVar

        # 從config讀取排程時間
        scheduled_hours = self.config_data.get('scheduled_hours', [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22])

        # 動態生成複選框
        current_row = 0
        for tab, sites in SITE_DATA.items():
            # 創建標籤標題
            tab_label = tb.Label(scrolled_content, text=f"{tab}:", font=("Helvetica", 10, "bold"))
            tab_label.grid(row=current_row, column=0, columnspan=15, sticky='w', pady=(10, 2))
            current_row += 1

            # 為該標籤下的地點創建複選框和時間選擇
            for site in sites:
                # 地點複選框
                site_var = tk.BooleanVar(value=True) # 預設全選
                self.site_vars[site] = site_var
                site_cb = tb.Checkbutton(scrolled_content, text=site, variable=site_var, bootstyle="primary")
                site_cb.grid(row=current_row, column=0, sticky='w', padx=10, pady=2)

                # 時間選擇複選框
                self.site_time_vars[site] = {}
                for i, hour in enumerate(scheduled_hours):
                    time_var = tk.BooleanVar(value=True) # 預設全選
                    self.site_time_vars[site][hour] = time_var
                    time_cb = tb.Checkbutton(scrolled_content, text=f"{hour:02d}", variable=time_var, bootstyle="info")
                    time_cb.grid(row=current_row, column=i+1, sticky='w', padx=2, pady=2)

                current_row += 1


        # 主要內容區域框架 (日誌 + 結果表格)
        main_content_frame = tb.Frame(self.root, padding=0)
        main_content_frame.pack(fill=BOTH, expand=True, padx=10, pady=(0, 10))
        main_content_frame.columnconfigure(0, weight=1)
        main_content_frame.rowconfigure(0, weight=2)
        main_content_frame.rowconfigure(1, weight=3)

        log_frame = tb.LabelFrame(main_content_frame, text="執行日誌", bootstyle=INFO)
        log_frame.grid(row=0, column=0, sticky=(N, S, W, E), pady=(0, 5))
        self.log_area = tb.ScrolledText(log_frame, wrap="word", height=8)
        self.log_area.pack(fill=BOTH, expand=True, padx=5, pady=5)
        self.log_area.config(state="disabled")
        self.log_area.tag_config('INFO', foreground=style.colors.fg if hasattr(style.colors, 'fg') else 'black')
        self.log_area.tag_config('WARNING', foreground=style.colors.warning)
        self.log_area.tag_config('ERROR', foreground=style.colors.danger)

        table_frame = tb.LabelFrame(main_content_frame, text="執行結果", bootstyle=SECONDARY)
        table_frame.grid(row=1, column=0, sticky=(N, S, W, E), pady=(5, 0))
        columns = ("Tab", "Site", "Result")
        self.tree = tb.Treeview(table_frame, columns=columns, show="headings")
        self.tree.heading('Tab', text='標籤/源地點', anchor="center")
        self.tree.heading('Site', text='地點/部門', anchor="w")
        self.tree.heading('Result', text='檢查結果', anchor="w")
        self.tree.column('Tab', width=120, anchor='center', stretch=False)
        self.tree.column('Site', width=200, anchor='w', stretch=False)
        self.tree.column('Result', width=350, anchor='w', stretch=True)
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        self.treescrolly = tb.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.treescrolly.pack(side=RIGHT, fill=Y)
        self.tree.configure(yscrollcommand=self.treescrolly.set)
        
        self.progress = tb.Progressbar(self.root, bootstyle=INFO + STRIPED, mode="indeterminate")

        self.message_queue = queue.Queue()
        self.automation_scheduler_thread = None
        self.automation_test_thread = None
        self.automation_status = AutomationStatus()
        self.all_results_for_export = []
        self._countdown_timer_id = None
        self._countdown_start_time = None
        self._countdown_total_duration = None
        self._countdown_reason = ""


        self.root.after(100, self.process_queue)

    # ==============================================
    # ====== (修正) 添加缺失的 on_closing 方法 ======
    # ==============================================
    def on_closing(self):
        """
        處理視窗關閉事件。
        """
        if self.automation_status.is_any_running():
            if messagebox.askokcancel("關閉程式", "自動化正在運行中。確定要停止並關閉嗎？"):
                self.stop_automation()
                # 給予一點時間讓停止請求生效
                self.root.after(500, self.root.destroy)
        else:
            self.root.destroy()
            
    # ==============================================
    # ====== 新增: UI 輔助方法 (全選/獲取選擇) ======
    # ==============================================
    def select_all_sites(self):
        for var in self.site_vars.values():
            var.set(True)

    def deselect_all_sites(self):
        for var in self.site_vars.values():
            var.set(False)

    def select_all_times(self):
        """全選所有地點的所有時間"""
        for site_times in self.site_time_vars.values():
            for time_var in site_times.values():
                time_var.set(True)

    def deselect_all_times(self):
        """全不選所有地點的所有時間"""
        for site_times in self.site_time_vars.values():
            for time_var in site_times.values():
                time_var.set(False)

    def get_selected_sites(self):
        """獲取所有被勾選的地點名稱列表"""
        selected = [site for site, var in self.site_vars.items() if var.get()]
        return selected

    def get_selected_sites_with_times(self):
        """獲取所有被勾選的地點及其對應的時間設定"""
        selected_sites_times = {}
        for site, site_var in self.site_vars.items():
            if site_var.get():  # 如果地點被選中
                selected_times = []
                for hour, time_var in self.site_time_vars[site].items():
                    if time_var.get():  # 如果該時間被選中
                        selected_times.append(hour)
                if selected_times:  # 只有當至少有一個時間被選中時才加入
                    selected_sites_times[site] = selected_times
        return selected_sites_times

    # ========================================================
    # (修正) update_button_states 和 _start/_stop_countdown
    # ========================================================
    def _update_countdown_timer_display(self):
        if self._countdown_start_time is None or self._countdown_total_duration is None:
            self._stop_countdown_timer()
            return
        elapsed_time = time.time() - self._countdown_start_time
        remaining_time = self._countdown_total_duration - elapsed_time
        if remaining_time > 0:
            hours, remainder = divmod(max(0, int(remaining_time)), 3600)
            minutes, seconds = divmod(remainder, 60)
            time_str = f"{hours:02d}:{minutes:02d}:{seconds:02d}"
            self.countdown_var.set(f"{self._countdown_reason}: {time_str}")
            self._countdown_timer_id = self.root.after(1000, self._update_countdown_timer_display)
        else:
            self.countdown_var.set("")
            self._stop_countdown_timer(clear_vars=False)

    def _start_countdown_timer(self, reason, duration, start_time):
        self._stop_countdown_timer()
        self._countdown_reason = reason
        self._countdown_total_duration = duration
        self._countdown_start_time = start_time
        if duration > 0:
            self.status_var.set("排程等待中...")
            self._update_countdown_timer_display()
        else:
            self.countdown_var.set("")

    def _stop_countdown_timer(self, clear_vars=True):
        if self._countdown_timer_id:
            self.root.after_cancel(self._countdown_timer_id)
            self._countdown_timer_id = None
        self.countdown_var.set("")
        if clear_vars:
            self._countdown_start_time = None
            self._countdown_total_duration = None
            self._countdown_reason = ""
    
    def update_button_states(self):
        is_running = self.automation_status.is_any_running()
        if is_running:
            self.start_button.config(state=DISABLED)
            self.test_button.config(state=DISABLED)
            self.stop_button.config(state=NORMAL)
            self.export_button.config(state=DISABLED)
            if self._countdown_timer_id is None and self.automation_status.is_any_running():
                 if self.progress.winfo_exists() and not self.progress.winfo_ismapped():
                      self.progress.pack(fill=X, padx=10, pady=(0,10), side=BOTTOM)
                      self.progress.start()
            elif self._countdown_timer_id is not None:
                 if self.progress.winfo_exists() and self.progress.winfo_ismapped():
                      self.progress.stop()
                      self.progress.pack_forget()
        else:
            self.start_button.config(state=NORMAL)
            self.test_button.config(state=NORMAL)
            self.stop_button.config(state=DISABLED)
            self.export_button.config(state=NORMAL)
            if self.progress.winfo_exists() and self.progress.winfo_ismapped():
                 self.progress.stop()
                 self.progress.pack_forget()
            if self._countdown_timer_id is None:
                 if self.automation_status.is_stop_requested():
                      self.status_var.set("已停止")
                 else:
                      self.status_var.set("待命中")
                      self.automation_status.reset_stop()

    # ====================================================
    # ====== 重大修改: start_... 方法需獲取已選地點 ======
    # ====================================================
    def start_scheduler(self):
        selected_sites_times = self.get_selected_sites_with_times()
        if not selected_sites_times:
            messagebox.showwarning("未選擇地點或時間", "請至少選擇一個地點並設定執行時間以開始排程。")
            return

        if self.automation_status.is_any_running():
            self.log_message("已有自動化週期正在運行，請稍後再開始排程。", 'WARNING')
            return

        try:
            self.config_data = load_config()
        except Exception as e:
            messagebox.showerror("配置錯誤", f"無法讀取設定檔。\n錯誤: {e}")
            return

        self.log_area.config(state=NORMAL)
        self.log_area.delete('1.0', END)
        self.log_area.config(state=DISABLED)
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.all_results_for_export.clear()

        self.automation_scheduler_thread = threading.Thread(target=automation_scheduler_thread, args=(self, selected_sites_times))
        self.automation_scheduler_thread.daemon = True
        self.automation_scheduler_thread.start()
        self.update_button_states()

    def start_test_run(self):
        selected_sites = self.get_selected_sites()
        if not selected_sites:
            messagebox.showwarning("未選擇地點", "請至少選擇一個地點以進行測試。")
            return

        if self.automation_status.is_any_running():
            self.log_message("已有自動化週期正在運行，請稍後再執行測試。", 'WARNING')
            return

        try:
            self.config_data = load_config()
        except Exception as e:
            messagebox.showerror("配置錯誤", f"無法讀取設定檔。\n錯誤: {e}")
            return

        self.log_area.config(state=NORMAL)
        self.log_area.delete('1.0', END)
        self.log_area.config(state=DISABLED)
        for item in self.tree.get_children():
            self.tree.delete(item)
        self.all_results_for_export.clear()

        self.automation_test_thread = threading.Thread(target=run_test_cycle_thread, args=(self, selected_sites))
        self.automation_test_thread.daemon = True
        self.automation_test_thread.start()
        self.update_button_states()

    def stop_automation(self):
        if not self.automation_status.is_any_running():
             self.log_message("目前沒有自動化正在運行。", 'INFO')
             return
        self.log_message("正在發送停止請求...", 'INFO')
        self.automation_status.request_stop()
        self.update_button_states()
        self.status_var.set("停止中...")
        self._stop_countdown_timer()

        # 等待執行緒結束後重置狀態
        def reset_after_stop():
            # 等待一段時間讓執行緒完全停止
            self.root.after(2000, self._reset_to_initial_state)

        reset_after_stop()

    def _reset_to_initial_state(self):
        """重置到初始狀態"""
        try:
            # 重置自動化狀態
            self.automation_status.reset_stop()

            # 重置UI狀態
            self.status_var.set("待命中")
            self.update_button_states()

            # 停止並隱藏進度條
            if hasattr(self, 'progress') and self.progress.winfo_exists():
                self.progress.stop()
                if self.progress.winfo_ismapped():
                    self.progress.pack_forget()

            # 停止倒數計時器
            self._stop_countdown_timer()

            self.log_message("已重置到初始狀態，可重新開始排程。", 'INFO')

        except Exception as e:
            self.log_message(f"重置狀態時發生錯誤: {e}", 'ERROR')

    def toggle_link_notification(self):
        """切換 LINK 通知功能"""
        enabled = self.link_notification_var.get()
        self.config_data['enable_line_notification'] = 'true' if enabled else 'false'

        # 更新配置檔案
        try:
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            if not config.has_section('LINK_API'):
                config.add_section('LINK_API')
            config.set('LINK_API', 'enable_line_notification', 'true' if enabled else 'false')

            with open('config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)

            status_text = "已啟用" if enabled else "已停用"
            self.log_message(f"LINK 通知功能{status_text}", 'INFO')

        except Exception as e:
            self.log_message(f"更新 LINK 通知設定失敗: {e}", 'ERROR')

    def toggle_automail_notification(self):
        """切換 AUTOMAIL 通知功能"""
        enabled = self.automail_notification_var.get()
        self.config_data['enable_automail'] = 'true' if enabled else 'false'

        # 更新配置檔案
        try:
            config = configparser.ConfigParser()
            config.read('config.ini', encoding='utf-8')
            if not config.has_section('AUTOMAIL_API'):
                config.add_section('AUTOMAIL_API')
            config.set('AUTOMAIL_API', 'enable_automail', 'true' if enabled else 'false')

            with open('config.ini', 'w', encoding='utf-8') as configfile:
                config.write(configfile)

            status_text = "已啟用" if enabled else "已停用"
            self.log_message(f"AUTOMAIL 通知功能{status_text}", 'INFO')

        except Exception as e:
            self.log_message(f"更新 AUTOMAIL 通知設定失敗: {e}", 'ERROR')

    def export_results_to_csv(self):
        if not self.all_results_for_export:
            messagebox.showinfo("匯出結果", "沒有結果可以匯出。")
            return
        fieldnames = [
            '時間戳', '主標籤', '主地點名稱', '主地點總人數', '主地點檢查結果',
            'ERC總體應確認人數', 'ERC總體待確認人數',
            'ERC部門標籤', 'ERC部門地點項目', 'ERC部門地點人數',
            'ERC子部門名稱', 'ERC子部門應確認人數', 'ERC子部門待確認人數',
            '詳細檢查結果'
        ]
        current_time = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        file_path = filedialog.asksaveasfilename(
            defaultextension=".csv",
            filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
            title="儲存結果到CSV檔案",
            initialfile=f"疏散平台監控結果_{current_time}.csv"
        )
        if not file_path:
            return
        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                for row_data in self.all_results_for_export:
                    cleaned_row = {field: row_data.get(field, '') for field in fieldnames}
                    writer.writerow(cleaned_row)
            messagebox.showinfo("匯出成功", f"結果已成功匯出至:\n{file_path}")
        except Exception as e:
            messagebox.showerror("匯出失敗", f"匯出結果時發生錯誤:\n{e}")

    def process_queue(self):
        while True:
            try:
                message = self.message_queue.get_nowait()
                current_timestamp = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                if message['type'] == 'log':
                    self.log_message(message['message'], message.get('level', 'INFO'))
                elif message['type'] == 'ui_update':
                    self._insert_table_row_ui(message['tab'], message['site'], message['result'])
                elif message['type'] == 'main_site_result':
                    detailed_result = {
                        '時間戳': current_timestamp, '主標籤': message.get('main_tab', ''),
                        '主地點名稱': message.get('main_site_name', ''),
                        '主地點總人數': message.get('main_site_total_people', ''),
                        '主地點檢查結果': message.get('detailed_check_result', '')
                    }
                    self.all_results_for_export.append(detailed_result)
                elif message['type'] == 'erc_overall_result':
                    detailed_result = {
                        '時間戳': current_timestamp,
                        '主標籤': message.get('main_tab', ''),
                        '主地點名稱': message.get('main_site_name', ''),
                        '主地點總人數': message.get('main_site_total_people', ''),
                        'ERC總體應確認人數': message.get('erc_total_sure', ''),
                        'ERC總體待確認人數': message.get('erc_total_wait', ''),
                        '詳細檢查結果': message.get('detailed_check_result', '')
                    }
                    self.all_results_for_export.append(detailed_result)

                elif message['type'] == 'erc_dept_site_item_result':
                    detailed_result = {
                        '時間戳': current_timestamp,
                        '主標籤': message.get('main_tab', ''),
                        '主地點名稱': message.get('main_site_name', ''),
                        '主地點總人數': message.get('main_site_total_people', ''),
                        'ERC部門標籤': message.get('erc_dept_tab', ''),
                        'ERC部門地點項目': message.get('erc_dept_site_item', ''),
                        'ERC部門地點人數': message.get('erc_dept_site_people', ''),
                        '詳細檢查結果': message.get('detailed_check_result', '')
                    }
                    self.all_results_for_export.append(detailed_result)

                elif message['type'] == 'erc_sub_dept_detail_result':
                    detailed_result = {
                        '時間戳': current_timestamp,
                        '主標籤': message.get('main_tab', ''),
                        '主地點名稱': message.get('main_site_name', ''),
                        '主地點總人數': message.get('main_site_total_people', ''),
                        'ERC部門標籤': message.get('erc_dept_tab', ''),
                        'ERC子部門名稱': message.get('erc_sub_dept_name', ''),
                        'ERC子部門應確認人數': message.get('erc_sub_dept_sure', ''),
                        'ERC子部門待確認人數': message.get('erc_sub_dept_wait', ''),
                        '詳細檢查結果': message.get('detailed_check_result', '')
                    }
                    self.all_results_for_export.append(detailed_result)
                elif message['type'] == 'countdown_start':
                     self._start_countdown_timer(message['reason'], message['duration'], message['start_time'])
                elif message['type'] == 'countdown_stop':
                     self._stop_countdown_timer(clear_vars=True)
            except queue.Empty:
                break
            except Exception as e:
                self.log_message(f"處理佇列消息時發生錯誤: {e}", 'ERROR')
        self.update_button_states()
        if self.root.winfo_exists():
            self.root.after(100, self.process_queue)
            
    def log_message(self, message, level='INFO'):
        if self.root.winfo_exists():
             self.root.after(0, self._insert_log, message, level)

    def _insert_log(self, message, level):
        if self.log_area.winfo_exists():
             self.log_area.config(state=NORMAL)
             self.log_area.insert(END, message + '\n', level)
             self.log_area.see(END)
             self.log_area.config(state=DISABLED)

    def _insert_table_row_ui(self, tab, site, result):
        if self.tree.winfo_exists():
             self.tree.insert('', END, values=(tab, site, result))

# ====== 主程式 ======
if __name__ == "__main__":
    root = tb.Window(themename="flatly")
    app = AutomationUI(root)
    root.mainloop()

