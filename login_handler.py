"""
登入處理類別 - 專門處理網頁登入帳號密碼填寫功能
"""

import time
from datetime import datetime
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException


class LoginHandler:
    """專門處理登入流程的類別"""
    
    def __init__(self, driver, config, logger_queue, screenshot_handler):
        """
        初始化登入處理器
        
        Args:
            driver: WebDriver 實例
            config: 配置字典
            logger_queue: 日誌隊列
            screenshot_handler: 截圖處理函數
        """
        self.driver = driver
        self.config = config
        self.q = logger_queue
        self.take_screenshot = screenshot_handler
        
        # 從配置中載入登入相關設定
        self.login_config = self._load_login_config()

        # 計時功能相關屬性
        self.step_start_time = None
        self.total_start_time = None
        
    def _load_login_config(self):
        """載入登入相關配置"""
        return {
            'fast_login_mode': self.config.get('fast_login_mode', True),
            'skip_login_screenshots': self.config.get('skip_login_screenshots', True),
            'disable_login_anomaly_checks': self.config.get('disable_login_anomaly_checks', False),
            'fast_sequential_input': self.config.get('fast_sequential_input', True),
            'ultra_fast_login': self.config.get('ultra_fast_login', True),
            'login_page_wait': float(self.config.get('login_page_wait', 1.0)),
            'account_input_interval': float(self.config.get('account_input_interval', 0.1)),
            'account_input_interval_normal': float(self.config.get('account_input_interval_normal', 0.2)),
            'password_wait_time': float(self.config.get('password_wait_time', 0.2)),
            'password_wait_time_normal': float(self.config.get('password_wait_time_normal', 0.5)),
            'login_click_interval': float(self.config.get('login_click_interval', 0.2)),
            'login_transition_wait': float(self.config.get('login_transition_wait', 0.3)),
            'login_element_timeout': int(self.config.get('login_element_timeout', 1)),
            'login_retry_count': int(self.config.get('login_retry_count', 2)),
        }
    
    def _log(self, message, level='INFO'):
        """記錄日誌"""
        self.q.put({'type': 'log', 'message': message, 'level': level})

    def _start_step_timing(self, step_name):
        """開始步驟計時"""
        self.step_start_time = time.time()
        self._log(f"⏱️ 開始計時: {step_name}")

    def _end_step_timing(self, step_name):
        """結束步驟計時並記錄耗時"""
        if self.step_start_time is not None:
            elapsed_time = time.time() - self.step_start_time
            self._log(f"⏱️ {step_name} 完成，耗時: {elapsed_time:.3f}s")
            self.step_start_time = None
            return elapsed_time
        return 0

    def _log_with_timing(self, message, level='INFO'):
        """記錄帶有時間戳的日誌"""
        if self.step_start_time is not None:
            elapsed = time.time() - self.step_start_time
            timestamp_msg = f"{message} (已耗時: {elapsed:.3f}s)"
        else:
            timestamp_msg = message
        self.q.put({'type': 'log', 'message': timestamp_msg, 'level': level})
    
    def _get_wait_time(self, ultra_key, normal_key):
        """根據超快速模式獲取等待時間"""
        if self.login_config['ultra_fast_login']:
            return self.login_config[ultra_key]
        else:
            return self.login_config[normal_key]
    
    def safe_input_text(self, element_id, text, description="輸入"):
        """安全的文字輸入方法，減少不必要的等待時間"""
        step_start = time.time()
        max_retries = self.login_config['login_retry_count']
        wait = WebDriverWait(self.driver, self.login_config['login_element_timeout'])

        for attempt in range(max_retries):
            attempt_start = time.time()
            try:
                if attempt == 0:
                    self._log(f"正在{description}...")
                else:
                    self._log(f"重試{description} (第{attempt + 1}次)...")

                # 等待元素可點擊
                element_wait_start = time.time()
                element = wait.until(EC.element_to_be_clickable((By.ID, element_id)))
                element_wait_time = time.time() - element_wait_start

                # 使用 JavaScript 快速輸入
                input_start = time.time()
                self.driver.execute_script(f"arguments[0].value = '{text}';", element)
                self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", element)
                input_time = time.time() - input_start

                # 驗證輸入
                verify_start = time.time()
                actual_value = element.get_attribute('value')
                verify_time = time.time() - verify_start

                attempt_total_time = time.time() - attempt_start

                if actual_value == text:
                    total_time = time.time() - step_start
                    self._log(f"{description}完成 (總耗時: {total_time:.3f}s, 元素等待: {element_wait_time:.3f}s, 輸入: {input_time:.3f}s, 驗證: {verify_time:.3f}s)")
                    return True
                else:
                    self._log(f"{description}驗證失敗，預期: {text}, 實際: {actual_value} (嘗試耗時: {attempt_total_time:.3f}s)", 'WARNING')

            except Exception as e:
                attempt_time = time.time() - attempt_start
                self._log(f"{description}失敗 (嘗試 {attempt + 1}, 耗時: {attempt_time:.3f}s): {e}", 'WARNING')
                if attempt < max_retries - 1:
                    time.sleep(0.5)

        total_time = time.time() - step_start
        self._log(f"❌ {description}最終失敗 (總耗時: {total_time:.3f}s)", 'ERROR')
        return False
    
    def fast_sequential_input(self):
        """快速連續輸入帳號和身分證"""
        total_start = time.time()
        try:
            self._log("開始快速連續輸入帳號和身分證...")

            wait = WebDriverWait(self.driver, self.login_config['login_element_timeout'])

            # 步驟1: 輸入帳號
            account_start = time.time()
            self._log("正在輸入帳號...")

            element_wait_start = time.time()
            account_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtAccount_E')))
            element_wait_time = time.time() - element_wait_start

            # 超快速清空並輸入帳號
            input_start = time.time()
            self.driver.execute_script(f"arguments[0].value = '{self.config['name']}';", account_element)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", account_element)
            input_time = time.time() - input_start

            account_total_time = time.time() - account_start
            self._log(f"帳號輸入完成 (耗時: {account_total_time:.3f}s, 元素等待: {element_wait_time:.3f}s, 輸入: {input_time:.3f}s)")

            # 根據超快速模式調整間隔時間
            interval_start = time.time()
            interval_time = self._get_wait_time('account_input_interval', 'account_input_interval_normal')
            time.sleep(interval_time)
            interval_actual_time = time.time() - interval_start
            self._log(f"帳號和身分證間隔等待完成 (設定: {interval_time:.3f}s, 實際: {interval_actual_time:.3f}s)")

            # 步驟2: 輸入身分證後四位
            idno_start = time.time()
            self._log("正在輸入身分證後四位...")

            element_wait_start = time.time()
            idno_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtIDNO')))
            element_wait_time = time.time() - element_wait_start

            # 超快速清空並輸入身分證
            input_start = time.time()
            self.driver.execute_script(f"arguments[0].value = '{self.config['password']}';", idno_element)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", idno_element)
            input_time = time.time() - input_start

            idno_total_time = time.time() - idno_start
            self._log(f"身分證後四位輸入完成 (耗時: {idno_total_time:.3f}s, 元素等待: {element_wait_time:.3f}s, 輸入: {input_time:.3f}s)")
            
            # 最終驗證
            verify_start = time.time()
            final_account = account_element.get_attribute('value')
            final_idno = idno_element.get_attribute('value')
            verify_time = time.time() - verify_start

            if final_account == self.config['name'] and final_idno == self.config['password']:
                self._log(f"✅ 快速連續輸入成功完成 (驗證耗時: {verify_time:.3f}s)")

                # 條件性截圖
                screenshot_start = time.time()
                if not self.login_config['skip_login_screenshots']:
                    self.take_screenshot("account_and_idno_entered", "帳號和身分證輸入完成")
                    screenshot_time = time.time() - screenshot_start
                    self._log(f"截圖完成 (耗時: {screenshot_time:.3f}s)")

                # 立即點擊下一步
                next_button_start = time.time()
                self._log("身分證輸入完成，立即點擊下一步...")

                button_wait_start = time.time()
                next_button = wait.until(EC.element_to_be_clickable((By.ID, 'btnNext_E')))
                button_wait_time = time.time() - button_wait_start

                click_start = time.time()
                next_button.click()
                click_time = time.time() - click_start

                next_button_total = time.time() - next_button_start
                self._log(f"✅ 下一步按鈕已點擊 (總耗時: {next_button_total:.3f}s, 等待: {button_wait_time:.3f}s, 點擊: {click_time:.3f}s)")

                # 條件性截圖
                screenshot_start = time.time()
                if not self.login_config['skip_login_screenshots']:
                    self.take_screenshot("next_clicked_fast", "快速點擊下一步後")
                    screenshot_time = time.time() - screenshot_start
                    self._log(f"截圖完成 (耗時: {screenshot_time:.3f}s)")

                total_time = time.time() - total_start
                self._log(f"🔄 帳號和身分證輸入流程總耗時: {total_time:.3f}s")
                return True
            else:
                total_time = time.time() - total_start
                self._log(f"❌ 輸入驗證失敗 - 帳號: {final_account}, 身分證: {final_idno} (總耗時: {total_time:.3f}s)", 'ERROR')
                return False

        except Exception as e:
            total_time = time.time() - total_start
            self._log(f"❌ 快速連續輸入失敗: {e} (總耗時: {total_time:.3f}s)", 'ERROR')
            return False
    
    def fast_password_input_and_login(self):
        """快速密碼輸入和登入"""
        total_start = time.time()
        try:
            # 等待密碼頁面載入
            page_load_start = time.time()
            self._log("等待密碼輸入頁面載入...")
            wait = WebDriverWait(self.driver, self.config['element_timeout_medium'])

            password_element = wait.until(EC.presence_of_element_located((By.ID, 'txtPassword_E')))
            page_load_time = time.time() - page_load_start
            self._log(f"密碼頁面載入完成 (耗時: {page_load_time:.3f}s)")

            # 根據超快速登入模式調整等待時間
            wait_start = time.time()
            wait_time = self._get_wait_time('password_wait_time', 'password_wait_time_normal')
            time.sleep(wait_time)
            actual_wait_time = time.time() - wait_start
            self._log(f"密碼輸入前等待完成 (設定: {wait_time:.3f}s, 實際: {actual_wait_time:.3f}s)")

            # 條件性截圖
            screenshot_start = time.time()
            if not self.login_config['skip_login_screenshots']:
                self.take_screenshot("password_page_loaded", "密碼頁面載入完成")
                screenshot_time = time.time() - screenshot_start
                self._log(f"截圖完成 (耗時: {screenshot_time:.3f}s)")

            # 快速輸入密碼
            password_input_start = time.time()
            self._log("正在快速輸入密碼...")

            element_wait_start = time.time()
            password_element = wait.until(EC.element_to_be_clickable((By.ID, 'txtPassword_E')))
            element_wait_time = time.time() - element_wait_start

            # 使用 JavaScript 快速輸入密碼
            input_start = time.time()
            self.driver.execute_script(f"arguments[0].value = '{self.config['password2']}';", password_element)
            self.driver.execute_script("arguments[0].dispatchEvent(new Event('input', {bubbles: true}));", password_element)
            input_time = time.time() - input_start

            password_input_total = time.time() - password_input_start
            self._log(f"密碼輸入完成 (總耗時: {password_input_total:.3f}s, 元素等待: {element_wait_time:.3f}s, 輸入: {input_time:.3f}s)")

            # 條件性截圖
            screenshot_start = time.time()
            if not self.login_config['skip_login_screenshots']:
                self.take_screenshot("password_entered", "輸入密碼後")
                screenshot_time = time.time() - screenshot_start
                self._log(f"截圖完成 (耗時: {screenshot_time:.3f}s)")

            # 間隔後點擊登入
            interval_start = time.time()
            interval_time = self.login_config['login_click_interval']
            time.sleep(interval_time)
            actual_interval_time = time.time() - interval_start
            self._log(f"登入點擊間隔等待完成 (設定: {interval_time:.3f}s, 實際: {actual_interval_time:.3f}s)")

            # 點擊登入按鈕
            login_click_start = time.time()
            self._log("點擊登入...")

            button_wait_start = time.time()
            login_button = wait.until(EC.element_to_be_clickable((By.ID, 'btnLogin_E')))
            button_wait_time = time.time() - button_wait_start

            click_start = time.time()
            login_button.click()
            click_time = time.time() - click_start

            login_click_total = time.time() - login_click_start
            self._log(f"登入按鈕已點擊 (總耗時: {login_click_total:.3f}s, 等待: {button_wait_time:.3f}s, 點擊: {click_time:.3f}s)")

            # 條件性截圖
            screenshot_start = time.time()
            if not self.login_config['skip_login_screenshots']:
                self.take_screenshot("login_clicked", "點擊登入後")
                screenshot_time = time.time() - screenshot_start
                self._log(f"截圖完成 (耗時: {screenshot_time:.3f}s)")

            # 登入後頁面跳轉等待時間
            transition_start = time.time()
            transition_time = self.login_config['login_transition_wait']
            time.sleep(transition_time)
            actual_transition_time = time.time() - transition_start
            self._log(f"登入後頁面跳轉等待完成 (設定: {transition_time:.3f}s, 實際: {actual_transition_time:.3f}s)")

            total_time = time.time() - total_start
            self._log(f"🔄 密碼輸入和登入流程總耗時: {total_time:.3f}s")
            return True

        except Exception as e:
            total_time = time.time() - total_start
            self._log(f"❌ 快速密碼輸入和登入失敗: {e} (總耗時: {total_time:.3f}s)", 'ERROR')
            return False
    
    def execute_login_process(self, anomaly_checker=None):
        """執行完整的登入流程"""
        total_start = time.time()
        try:
            self._log("\n--- 開始登入流程 (快速模式) ---")

            # 登入頁面載入完成後等待
            page_wait_start = time.time()
            wait_time = self.login_config['login_page_wait']
            self._log(f"登入頁面載入完成，{wait_time}秒後開始輸入...")
            time.sleep(wait_time)
            actual_wait_time = time.time() - page_wait_start
            self._log(f"登入頁面等待完成 (設定: {wait_time:.3f}s, 實際: {actual_wait_time:.3f}s)")

            # 條件性異常檢測
            if not self.login_config['disable_login_anomaly_checks'] and anomaly_checker:
                anomaly_start = time.time()
                self._log("開始登入頁面異常檢測...")
                # login_page_anomalies = anomaly_checker("登入頁面")
                # 這裡需要異常報告功能，暫時跳過
                anomaly_time = time.time() - anomaly_start
                self._log(f"登入頁面異常檢測完成 (耗時: {anomaly_time:.3f}s)")

            # 快速連續輸入帳號和身分證
            account_phase_start = time.time()
            self._log("🔄 階段1: 開始帳號和身分證輸入...")
            if not self.fast_sequential_input():
                raise Exception("快速連續輸入失敗")
            account_phase_time = time.time() - account_phase_start
            self._log(f"✅ 階段1完成: 帳號和身分證輸入 (總耗時: {account_phase_time:.3f}s)")

            # 階段間等待時間
            transition_start = time.time()
            transition_time = self.login_config['login_transition_wait']
            time.sleep(transition_time)
            actual_transition_time = time.time() - transition_start
            self._log(f"階段間等待完成 (設定: {transition_time:.3f}s, 實際: {actual_transition_time:.3f}s)")

            # 快速密碼輸入和登入
            password_phase_start = time.time()
            self._log("🔄 階段2: 開始密碼輸入和登入...")
            if not self.fast_password_input_and_login():
                raise Exception("快速密碼輸入和登入失敗")
            password_phase_time = time.time() - password_phase_start
            self._log(f"✅ 階段2完成: 密碼輸入和登入 (總耗時: {password_phase_time:.3f}s)")

            total_time = time.time() - total_start
            self._log(f"🎉 登入流程全部完成 - 總耗時: {total_time:.3f}s")
            self._log("--- 登入流程結束 ---")
            return True

        except Exception as e:
            total_time = time.time() - total_start
            self._log(f"錯誤: 登入流程發生未預期錯誤: {e} (總耗時: {total_time:.3f}s)", 'ERROR')
            return False
